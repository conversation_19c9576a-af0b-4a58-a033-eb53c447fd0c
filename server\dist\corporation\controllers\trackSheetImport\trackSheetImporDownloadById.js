"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.downloadTrackSheetImportById = void 0;
const prismaClient_1 = __importDefault(require("../../../utils/prismaClient"));
const helpers_1 = require("../../../utils/helpers");
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const downloadTrackSheetImportById = async (req, res) => {
    try {
        const { id } = req.params;
        const importRecord = await prismaClient_1.default.trackSheetImport.findUnique({
            where: { id },
        });
        if (!importRecord || !importRecord.location) {
            return res.status(404).json({ message: "Import file not found" });
        }
        // Resolve the absolute path to the file
        const filePath = path_1.default.resolve(importRecord.location);
        if (!fs_1.default.existsSync(filePath)) {
            return res.status(404).json({ message: "File does not exist on server" });
        }
        // Set headers and send the file for download
        return res.download(filePath, importRecord.fileName);
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.downloadTrackSheetImportById = downloadTrackSheetImportById;
//# sourceMappingURL=trackSheetImporDownloadById.js.map