"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewManifest = void 0;
const viewManifest = async (req, res) => {
    try {
        const { id } = req.params;
        if (!id) {
            return res.status(400).json({ error: "trackSheetId is required." });
        }
        const result = await prisma.manifestDetailsSchema.findUnique({
            where: { trackSheetId: Number(id) },
            select: {
                manifestStatus: true,
                manifestDate: true,
                manifestNotes: true,
                actionRequired: true,
            },
        });
        return res.status(200).json({ success: true, data: result });
    }
    catch (err) {
        console.error(err);
        return res.status(500).json({ error: "Failed to get manifest details" });
    }
};
exports.viewManifest = viewManifest;
//# sourceMappingURL=viewManifest.js.map