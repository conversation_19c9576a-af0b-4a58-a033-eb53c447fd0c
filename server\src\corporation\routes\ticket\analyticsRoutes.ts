import { Router } from "express";
import { authenticate } from "../../../middleware/authentication";

// Import analytics controllers
import { getTicketsAnalyticsOverview } from "../../controllers/ticket/analytics/overview";
import { getTicketDistributionByStage } from "../../controllers/ticket/analytics/distribution";
import { getClosureRateByStage } from "../../controllers/ticket/analytics/closureRate";
import { getAverageTimeByStage } from "../../controllers/ticket/analytics/timeAnalysis";
import { getClosureRateInsights, getResolutionTimeInsights } from "../../controllers/ticket/analytics/insights";

const router = Router();

router.get("/overview",
    // authenticate,
    getTicketsAnalyticsOverview
);

router.get("/distribution-by-stage", 
    // authenticate,
    getTicketDistributionByStage
);

router.get("/closure-rate-by-stage",
    // authenticate, 
    getClosureRateByStage
);

router.get("/average-time-by-stage",
    // authenticate,
    getAverageTimeByStage
);

// Insights APIs
router.get("/insights/closure-rate",
    // authenticate,
    getClosureRateInsights
);

router.get("/insights/resolution-time",
    // authenticate,
    getResolutionTimeInsights
);

export default router;
