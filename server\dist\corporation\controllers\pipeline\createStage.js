"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.addPipelineStages = void 0;
const helpers_1 = require("../../../utils/helpers");
const prismaClient_1 = __importDefault(require("../../../utils/prismaClient"));
const addPipelineStages = async (req, res) => {
    const { id: pipelineId } = req.params;
    const stages = req.body;
    if (!Array.isArray(stages)) {
        return res
            .status(400)
            .json({ success: false, message: "Body must be an array of stages" });
    }
    try {
        // Get the max current order for this specific pipeline
        const maxOrder = await prismaClient_1.default.pipelineStage.aggregate({
            where: { pipelineId },
            _max: { order: true },
        });
        let nextOrder = (maxOrder._max.order ?? -1) + 1;
        const existingStagesByName = await prismaClient_1.default.pipelineStage.findMany({
            where: {
                pipelineId,
                name: { in: stages.map((s) => s.name) },
            },
        });
        const existingNamesSet = new Set(existingStagesByName.map((s) => s.name));
        const stagesToCreate = [];
        for (const stage of stages) {
            if (existingNamesSet.has(stage.name)) {
                const existing = existingStagesByName.find((s) => s.name === stage.name);
                await prismaClient_1.default.pipelineStage.update({
                    where: { id: existing.id },
                    data: { order: existing.order + 1 },
                });
            }
            else {
                stagesToCreate.push({
                    ...stage,
                    pipelineId,
                    order: nextOrder,
                    createdBy: stage.createdBy,
                });
                nextOrder += 1;
            }
        }
        if (stagesToCreate.length > 0) {
            await prismaClient_1.default.pipelineStage.createMany({ data: stagesToCreate });
        }
        return res
            .status(200)
            .json({ success: true, message: "Stages processed successfully", stagesToCreate });
    }
    catch (error) {
        console.error("Error in addPipelineStages:", error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.addPipelineStages = addPipelineStages;
//# sourceMappingURL=createStage.js.map