{"info": {"_postman_id": "a8f4d5e2-1a3b-4c5d-9e8f-7g6h5i4j3k2l", "name": "Ticketing Analytics API Collection", "description": "Comprehensive collection for testing ticketing analytics and reporting endpoints. This collection includes all analytics APIs with pre-configured requests, query parameters, and example responses.\n\n**Base URL:** `{{baseUrl}}/api/tickets`\n\n**Authentication:** <PERSON><PERSON> (when enabled)\n\n**Common Query Parameters:**\n- `dateFrom`: ISO 8601 date string (e.g., \"2024-01-01T00:00:00Z\")\n- `dateTo`: ISO 8601 date string (e.g., \"2024-12-31T23:59:59Z\")\n- `tags`: Comma-separated list of tag IDs\n- `assignedTo`: User ID or username\n- `stageId`: Pipeline stage ID\n- `priority`: Ticket priority (HIGH, MEDIUM, LOW)\n- `corporationId`: Corporation ID for multi-tenancy", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "12345678"}, "item": [{"name": "Analytics Overview", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has success field\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('success');", "    pm.expect(jsonData.success).to.eql(true);", "});", "", "pm.test(\"Response contains required data fields\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('totalTickets');", "    pm.expect(jsonData.data).to.have.property('closedTickets');", "    pm.expect(jsonData.data).to.have.property('openTickets');", "    pm.expect(jsonData.data).to.have.property('averageTimeToClose');", "    pm.expect(jsonData.data).to.have.property('closureRate');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}, {"key": "Authorization", "value": "Bearer {{authToken}}", "disabled": true}], "url": {"raw": "{{baseUrl}}/api/tickets/analytics/overview?corporationId={{corporationId}}&dateFrom={{dateFrom}}&dateTo={{dateTo}}", "host": ["{{baseUrl}}"], "path": ["api", "tickets", "analytics", "overview"], "query": [{"key": "corporationId", "value": "{{corporationId}}", "description": "Corporation ID for multi-tenancy"}, {"key": "dateFrom", "value": "{{dateFrom}}", "description": "Start date for filtering (ISO 8601 format)", "disabled": true}, {"key": "dateTo", "value": "{{dateTo}}", "description": "End date for filtering (ISO 8601 format)", "disabled": true}, {"key": "tags", "value": "urgent,bug", "description": "Comma-separated tag IDs", "disabled": true}, {"key": "priority", "value": "HIGH", "description": "Ticket priority level", "disabled": true}, {"key": "assignedTo", "value": "user123", "description": "User ID or username", "disabled": true}]}, "description": "Get high-level metrics and KPIs for tickets including total counts, closure rates, and average resolution times."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/tickets/analytics/overview?corporationId=1", "host": ["{{baseUrl}}"], "path": ["api", "tickets", "analytics", "overview"], "query": [{"key": "corporationId", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"data\": {\n    \"totalTickets\": 1250,\n    \"closedTickets\": 1000,\n    \"openTickets\": 250,\n    \"averageTimeToClose\": 145.5,\n    \"averageTimeToCloseUnit\": \"hours\",\n    \"closureRate\": 80.0,\n    \"ticketsCreatedInPeriod\": 300,\n    \"ticketsClosedInPeriod\": 280\n  }\n}"}]}, {"name": "Distribution by Stage", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response is an array\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.be.an('array');", "});", "", "pm.test(\"Each stage has required fields\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.data.length > 0) {", "        pm.expect(jsonData.data[0]).to.have.property('stageId');", "        pm.expect(jsonData.data[0]).to.have.property('stageName');", "        pm.expect(jsonData.data[0]).to.have.property('ticketCount');", "        pm.expect(jsonData.data[0]).to.have.property('percentage');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/tickets/analytics/distribution-by-stage?corporationId={{corporationId}}", "host": ["{{baseUrl}}"], "path": ["api", "tickets", "analytics", "distribution-by-stage"], "query": [{"key": "corporationId", "value": "{{corporationId}}"}, {"key": "dateFrom", "value": "2024-01-01T00:00:00Z", "disabled": true}, {"key": "dateTo", "value": "2024-12-31T23:59:59Z", "disabled": true}, {"key": "priority", "value": "HIGH", "disabled": true}]}, "description": "Shows ticket distribution across pipeline stages with counts and percentages."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/tickets/analytics/distribution-by-stage?corporationId=1", "host": ["{{baseUrl}}"], "path": ["api", "tickets", "analytics", "distribution-by-stage"], "query": [{"key": "corporationId", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"data\": [\n    {\n      \"stageId\": \"stage-1\",\n      \"stageName\": \"To Do\",\n      \"stageOrder\": 1,\n      \"ticketCount\": 150,\n      \"percentage\": 12.0\n    },\n    {\n      \"stageId\": \"stage-2\",\n      \"stageName\": \"In Progress\",\n      \"stageOrder\": 2,\n      \"ticketCount\": 75,\n      \"percentage\": 6.0\n    },\n    {\n      \"stageId\": \"stage-3\",\n      \"stageName\": \"Done\",\n      \"stageOrder\": 3,\n      \"ticketCount\": 1025,\n      \"percentage\": 82.0\n    }\n  ]\n}"}]}, {"name": "Closure Rate by Stage", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Each stage has closure rate data\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.data.length > 0) {", "        pm.expect(jsonData.data[0]).to.have.property('closureRate');", "        pm.expect(jsonData.data[0]).to.have.property('ticketsEntered');", "        pm.expect(jsonData.data[0]).to.have.property('ticketsCompleted');", "        pm.expect(jsonData.data[0]).to.have.property('averageTimeInStage');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/tickets/analytics/closure-rate-by-stage?corporationId={{corporationId}}", "host": ["{{baseUrl}}"], "path": ["api", "tickets", "analytics", "closure-rate-by-stage"], "query": [{"key": "corporationId", "value": "{{corporationId}}"}, {"key": "dateFrom", "value": "2024-01-01T00:00:00Z", "disabled": true}, {"key": "stageId", "value": "stage-1", "description": "Filter by specific stage", "disabled": true}, {"key": "assignedTo", "value": "user123", "disabled": true}]}, "description": "Success/completion rate for each pipeline stage with time tracking metrics."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/tickets/analytics/closure-rate-by-stage?corporationId=1", "host": ["{{baseUrl}}"], "path": ["api", "tickets", "analytics", "closure-rate-by-stage"], "query": [{"key": "corporationId", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"data\": [\n    {\n      \"stageId\": \"stage-1\",\n      \"stageName\": \"To Do\",\n      \"stageOrder\": 1,\n      \"ticketsEntered\": 300,\n      \"ticketsCompleted\": 285,\n      \"closureRate\": 95.0,\n      \"averageTimeInStage\": 24.5,\n      \"timeUnit\": \"hours\"\n    },\n    {\n      \"stageId\": \"stage-2\",\n      \"stageName\": \"In Progress\",\n      \"stageOrder\": 2,\n      \"ticketsEntered\": 285,\n      \"ticketsCompleted\": 270,\n      \"closureRate\": 94.7,\n      \"averageTimeInStage\": 72.3,\n      \"timeUnit\": \"hours\"\n    }\n  ]\n}"}]}, {"name": "Average Time by Stage", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Each stage has time statistics\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.data.length > 0) {", "        pm.expect(jsonData.data[0]).to.have.property('averageTime');", "        pm.expect(jsonData.data[0]).to.have.property('medianTime');", "        pm.expect(jsonData.data[0]).to.have.property('minTime');", "        pm.expect(jsonData.data[0]).to.have.property('maxTime');", "        pm.expect(jsonData.data[0]).to.have.property('sampleSize');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/tickets/analytics/average-time-by-stage?corporationId={{corporationId}}", "host": ["{{baseUrl}}"], "path": ["api", "tickets", "analytics", "average-time-by-stage"], "query": [{"key": "corporationId", "value": "{{corporationId}}"}, {"key": "stageId", "value": "stage-1", "description": "Analyze specific stage only", "disabled": true}, {"key": "dateFrom", "value": "2024-01-01T00:00:00Z", "disabled": true}, {"key": "dateTo", "value": "2024-12-31T23:59:59Z", "disabled": true}]}, "description": "Average time spent in each pipeline stage with detailed statistics."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/tickets/analytics/average-time-by-stage?corporationId=1", "host": ["{{baseUrl}}"], "path": ["api", "tickets", "analytics", "average-time-by-stage"], "query": [{"key": "corporationId", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"data\": [\n    {\n      \"stageId\": \"stage-1\",\n      \"stageName\": \"To Do\",\n      \"stageOrder\": 1,\n      \"averageTime\": 24.5,\n      \"medianTime\": 18.2,\n      \"minTime\": 2.1,\n      \"maxTime\": 168.0,\n      \"timeUnit\": \"hours\",\n      \"sampleSize\": 285\n    },\n    {\n      \"stageId\": \"stage-2\",\n      \"stageName\": \"In Progress\",\n      \"stageOrder\": 2,\n      \"averageTime\": 72.3,\n      \"medianTime\": 64.0,\n      \"minTime\": 8.5,\n      \"maxTime\": 240.0,\n      \"timeUnit\": \"hours\",\n      \"sampleSize\": 270\n    }\n  ]\n}"}]}, {"name": "Closure Rate Insights", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has insights structure\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('overallClosureRate');", "    pm.expect(jsonData.data).to.have.property('insights');", "    pm.expect(jsonData.data.insights).to.be.an('array');", "});", "", "pm.test(\"Insights have performance categories\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.data.insights.length > 0) {", "        pm.expect(jsonData.data.insights[0]).to.have.property('category');", "        pm.expect(jsonData.data.insights[0]).to.have.property('stages');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/tickets/analytics/insights/closure-rate?corporationId={{corporationId}}", "host": ["{{baseUrl}}"], "path": ["api", "tickets", "analytics", "insights", "closure-rate"], "query": [{"key": "corporationId", "value": "{{corporationId}}"}, {"key": "dateFrom", "value": "2024-01-01T00:00:00Z", "disabled": true}, {"key": "tags", "value": "critical,urgent", "disabled": true}]}, "description": "Insights on closure rate performance with categorization (HIGH/MEDIUM/LOW performers)."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/tickets/analytics/insights/closure-rate?corporationId=1", "host": ["{{baseUrl}}"], "path": ["api", "tickets", "analytics", "insights", "closure-rate"], "query": [{"key": "corporationId", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"data\": {\n    \"overallClosureRate\": 80.0,\n    \"insights\": [\n      {\n        \"category\": \"HIGH_PERFORMER\",\n        \"stages\": [\n          {\n            \"stageId\": \"stage-1\",\n            \"stageName\": \"To Do\",\n            \"closureRate\": 95.0,\n            \"recommendation\": \"Excellent performance, maintain current processes\"\n          }\n        ]\n      },\n      {\n        \"category\": \"MEDIUM_PERFORMER\",\n        \"stages\": [\n          {\n            \"stageId\": \"stage-2\",\n            \"stageName\": \"In Progress\",\n            \"closureRate\": 75.5,\n            \"recommendation\": \"Consider process improvements or additional resources\"\n          }\n        ]\n      },\n      {\n        \"category\": \"LOW_PERFORMER\",\n        \"stages\": [\n          {\n            \"stageId\": \"stage-3\",\n            \"stageName\": \"Review\",\n            \"closureRate\": 45.2,\n            \"recommendation\": \"Requires immediate attention and process review\"\n          }\n        ]\n      }\n    ]\n  }\n}"}]}, {"name": "Resolution Time Insights", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has resolution time insights\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.have.property('overallStats');", "    pm.expect(jsonData.data).to.have.property('byPriority');", "    pm.expect(jsonData.data).to.have.property('recommendations');", "});", "", "pm.test(\"Priority breakdown exists\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.data.byPriority).to.be.an('array');", "    if (jsonData.data.byPriority.length > 0) {", "        pm.expect(jsonData.data.byPriority[0]).to.have.property('priority');", "        pm.expect(jsonData.data.byPriority[0]).to.have.property('averageTime');", "        pm.expect(jsonData.data.byPriority[0]).to.have.property('performance');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json"}], "url": {"raw": "{{baseUrl}}/api/tickets/analytics/insights/resolution-time?corporationId={{corporationId}}", "host": ["{{baseUrl}}"], "path": ["api", "tickets", "analytics", "insights", "resolution-time"], "query": [{"key": "corporationId", "value": "{{corporationId}}"}, {"key": "priority", "value": "HIGH", "description": "Filter by specific priority", "disabled": true}, {"key": "dateFrom", "value": "2024-01-01T00:00:00Z", "disabled": true}, {"key": "dateTo", "value": "2024-12-31T23:59:59Z", "disabled": true}]}, "description": "Analysis of ticket resolution times with actionable insights and performance benchmarks."}, "response": [{"name": "Successful Response", "originalRequest": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/api/tickets/analytics/insights/resolution-time?corporationId=1", "host": ["{{baseUrl}}"], "path": ["api", "tickets", "analytics", "insights", "resolution-time"], "query": [{"key": "corporationId", "value": "1"}]}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "cookie": [], "body": "{\n  \"success\": true,\n  \"data\": {\n    \"overallStats\": {\n      \"averageResolutionTime\": 145.5,\n      \"medianResolutionTime\": 120.0,\n      \"timeUnit\": \"hours\"\n    },\n    \"byPriority\": [\n      {\n        \"priority\": \"HIGH\",\n        \"averageTime\": 48.2,\n        \"medianTime\": 36.0,\n        \"sampleSize\": 125,\n        \"performance\": \"GOOD\",\n        \"benchmark\": 72.0\n      },\n      {\n        \"priority\": \"MEDIUM\",\n        \"averageTime\": 120.5,\n        \"medianTime\": 96.0,\n        \"sampleSize\": 580,\n        \"performance\": \"AVERAGE\",\n        \"benchmark\": 120.0\n      },\n      {\n        \"priority\": \"LOW\",\n        \"averageTime\": 240.8,\n        \"medianTime\": 192.0,\n        \"sampleSize\": 545,\n        \"performance\": \"SLOW\",\n        \"benchmark\": 168.0\n      }\n    ],\n    \"recommendations\": [\n      \"High priority tickets are meeting SLA requirements\",\n      \"Medium priority tickets performance is within acceptable range\",\n      \"Low priority tickets are taking longer than benchmark - consider resource allocation\"\n    ]\n  }\n}"}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set default date range if not provided", "if (!pm.variables.get(\"dateFrom\")) {", "    const oneMonthAgo = new Date();", "    oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);", "    pm.variables.set(\"dateFrom\", oneMonthAgo.toISOString());", "}", "", "if (!pm.variables.get(\"dateTo\")) {", "    pm.variables.set(\"dateTo\", new Date().toISOString());", "}", "", "// Set default corporation ID if not provided", "if (!pm.variables.get(\"corporationId\")) {", "    pm.variables.set(\"corporationId\", \"1\");", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test for all endpoints", "pm.test(\"Response time is acceptable\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});", "", "pm.test(\"Content-Type is application/json\", function () {", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/json\");", "});"]}}], "variable": [{"key": "baseUrl", "value": "http://localhost:3000", "description": "Base URL for the API server"}, {"key": "corporationId", "value": "1", "description": "Default corporation ID for testing"}, {"key": "authToken", "value": "your-jwt-token-here", "description": "JWT token for authentication (when enabled)"}, {"key": "dateFrom", "value": "", "description": "Start date for filtering (automatically set to 1 month ago if empty)"}, {"key": "dateTo", "value": "", "description": "End date for filtering (automatically set to current date if empty)"}]}