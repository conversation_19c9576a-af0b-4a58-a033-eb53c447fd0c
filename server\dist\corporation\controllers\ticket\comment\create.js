"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createComment = void 0;
const helpers_1 = require("../../../../utils/helpers");
const createComment = async (req, res) => {
    try {
        const { ticketId, content, createdBy } = req.body;
        if (!ticketId || !content) {
            return res.status(400).json({
                success: false,
                message: "Ticket ID and content are required",
            });
        }
        // Always use createdBy from frontend, fallback to 'system'
        const username = createdBy || "system";
        const comment = await prisma.comment.create({
            data: {
                ticketId,
                content,
                createdBy: username,
            },
        });
        return res.status(201).json({
            success: true,
            message: "Comment created successfully",
            data: comment,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.createComment = createComment;
//# sourceMappingURL=create.js.map