"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getOrderBy = getOrderBy;
function getOrderBy(sortByArr, orderArr, allowedFields) {
    if (!Array.isArray(sortByArr))
        sortByArr = [sortByArr];
    if (!Array.isArray(orderArr))
        orderArr = [orderArr];
    const orderByArray = sortByArr.map((sortBy, idx) => {
        let field = allowedFields.includes(sortBy) ? sortBy : allowedFields[0];
        let sortOrder = orderArr[idx] === "desc" ? "desc" : "asc";
        // Handle nested field sorting (e.g., client.client_name)
        if (field.includes(".")) {
            const [relation, relationField] = field.split(".");
            return {
                [relation]: {
                    [relationField]: sortOrder,
                },
            };
        }
        return { [field]: sortOrder };
    });
    return orderByArray;
}
//# sourceMappingURL=sortHelper.js.map