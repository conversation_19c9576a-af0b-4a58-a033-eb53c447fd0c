"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_1 = require("../../../middleware/authentication");
const view_1 = require("../../controllers/pipelineStage.ts/view");
const delete_1 = require("../../controllers/pipelineStage.ts/delete");
const update_1 = require("../../controllers/pipelineStage.ts/update");
const router = (0, express_1.Router)();
router.get('/', authentication_1.authenticate, view_1.viewPipelineStage);
router.get('/:id', authentication_1.authenticate, view_1.viewPipelineStageById);
router.delete('/:id', authentication_1.authenticate, delete_1.deletePipelineStage);
router.put('/:id', authentication_1.authenticate, update_1.updatePipelineStage);
exports.default = router;
//# sourceMappingURL=pipelineStageRoutes.js.map