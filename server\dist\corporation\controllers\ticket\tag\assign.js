"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.assignTagsToTicket = void 0;
const helpers_1 = require("../../../../utils/helpers");
const assignTagsToTicket = async (req, res) => {
    try {
        const { ticketId, tagIds } = req.body;
        if (!ticketId || !Array.isArray(tagIds)) {
            return res.status(400).json({
                success: false,
                message: "Ticket ID and array of tag IDs are required",
            });
        }
        // Verify that the ticket exists
        const ticket = await prisma.ticket.findFirst({
            where: {
                id: ticketId,
                deletedAt: null,
            },
        });
        if (!ticket) {
            return res.status(404).json({
                success: false,
                message: "Ticket not found",
            });
        }
        // Verify that all tag IDs exist
        const existingTags = await prisma.tag.findMany({
            where: {
                id: {
                    in: tagIds,
                },
                deletedAt: null,
            },
            select: {
                id: true,
            },
        });
        if (existingTags.length !== tagIds.length) {
            return res.status(400).json({
                success: false,
                message: "One or more tags not found",
            });
        }
        // Update the ticket with the new tag IDs
        const updatedTicket = await prisma.ticket.update({
            where: { id: ticketId },
            data: {
                tags: tagIds,
                updatedBy: req.body?.createdBy || "system",
            },
        });
        return res.status(200).json({
            success: true,
            message: "Tags assigned successfully",
            data: updatedTicket,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.assignTagsToTicket = assignTagsToTicket;
//# sourceMappingURL=assign.js.map