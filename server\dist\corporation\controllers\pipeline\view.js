"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewPipelineWorkType = exports.viewPipelineByName = exports.viewPipelineById = exports.viewPipeline = void 0;
const helpers_1 = require("../../../utils/helpers");
const prismaClient_1 = __importDefault(require("../../../utils/prismaClient"));
const client_1 = require("@prisma/client");
const viewPipeline = async (req, res) => {
    try {
        const data = await prismaClient_1.default.pipeline.findMany({
            where: {
                deletedAt: null,
            },
            orderBy: {
                createdAt: "desc",
            },
            include: {
                stages: {
                    where: {
                        deletedAt: null,
                    },
                    orderBy: {
                        order: "asc",
                    },
                },
            },
        });
        return res.status(200).json({
            data: data,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewPipeline = viewPipeline;
const viewPipelineById = async (req, res) => {
    try {
        const { id } = req.params;
        if (!id) {
            return res.status(400).json({ error: "Pipeline id is required" });
        }
        const data = await prismaClient_1.default.pipeline.findUnique({
            where: {
                id: id,
                deletedAt: null,
            },
            include: {
                stages: {
                    where: {
                        deletedAt: null,
                    },
                    orderBy: {
                        order: "asc",
                    },
                },
            },
        });
        if (!data) {
            return res.status(404).json({ error: "Pipeline not found" });
        }
        return res.status(200).json({ data });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewPipelineById = viewPipelineById;
const viewPipelineByName = async (req, res) => {
    try {
        const { name } = req.params;
        if (!name) {
            return res.status(400).json({ error: "Pipeline name is required" });
        }
        // const sql = `select * from pipelines where name = '${name}'`;
        // const data = await prisma.$queryRawUnsafe(sql);
        const data = await prismaClient_1.default.pipeline.findFirst({
            where: { name },
        });
        if (!data) {
            return res.status(404).json({ error: "Pipeline not found" });
        }
        return res.status(200).json({ data });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewPipelineByName = viewPipelineByName;
const viewPipelineWorkType = async (req, res) => {
    try {
        // Check if we're getting all work types or filtering by a specific work type
        const { workType } = req.params;
        if (workType) {
            // If workType parameter is provided, filter pipelines by that work type
            const pipelines = await prismaClient_1.default.pipeline.findMany({
                where: {
                    workType: workType,
                    deletedAt: null,
                },
                select: {
                    workType: true,
                },
                distinct: ['workType'],
                orderBy: {
                    workType: 'asc',
                },
            });
            return res.status(200).json({
                data: pipelines.map((item) => item.workType),
            });
        }
        else {
            // If no workType parameter, return all available work types from the enum
            const allWorkTypes = Object.values(client_1.Worktype);
            return res.status(200).json({
                data: allWorkTypes,
            });
        }
    }
    catch (error) {
        console.error("Error in viewPipelineWorkType:", error);
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewPipelineWorkType = viewPipelineWorkType;
//# sourceMappingURL=view.js.map