// This model is a placeholder for testing purposes and does not represent any real data structure.
// Ensure that the file name matches the model name
// The model name should be unique across the Prisma schema
// The model name should be in PascalCase
// We should have the map attribute to ensure the table name is consistent with the database naming conventions
// The database field names should be in snake_case
// Ensure there is proper indentation and spacing for readability

// The model should be singular, not plural as it represents a single entity
model DummyModel {
    // the id field should be a unique identifier
    id          String          @id @default(uuid())
    
    firstField  String?         @map("first_field")
    secondField String?         @map("second_field")

    // The following 6 fields are to be included in all models for consistency.
    createdAt   DateTime        @map("created_at") @default(now())             
    createdBy   String?         @map("created_by")
    updatedAt   DateTime?       @map("updated_at") @updatedAt 
    updatedBy   String?         @map("updated_by")
    deletedAt   DateTime?       @map("deleted_at")
    deletedBy   String?         @map("deleted_by")

    // The table name should be plural to follow the convention of naming tables in the database
    @@map("dummy_models")
}

