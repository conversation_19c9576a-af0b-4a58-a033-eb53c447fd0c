"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const create_1 = require("../../controllers/customFilepath/create");
const update_1 = require("../../controllers/customFilepath/update");
const view_1 = require("../../controllers/customFilepath/view");
const delete_1 = require("../../controllers/customFilepath/delete");
const router = (0, express_1.Router)();
// Create
router.post("/cffpc/create", create_1.createClientFTPFilePathConfig);
// Update
router.put("/cffpc/update", update_1.updateClientFTPFilePathConfig);
// View (by clientId)
router.get("/cffpc/view", view_1.viewClientFTPFilePathConfig);
router.get("/", view_1.viewAllClientFTPFilePathConfigs);
// Delete
router.delete("/cffpc/delete", delete_1.deleteClientFTPFilePathConfig);
exports.default = router;
//# sourceMappingURL=customFilepathRoutes.js.map