import { handleError } from "../../../../utils/helpers";

/**
 * Get average time analysis by pipeline stage
 * Provides detailed time statistics for each stage
 * 
 * @param req - Express request object with query parameters for filtering
 * @param res - Express response object
 * @returns Time analysis data with average, median, min, max times per stage
 */
export const getAverageTimeByStage = async (req: any, res: any) => {
  try {
    const {
      dateFrom,
      dateTo,
      tags,
      assignedTo,
      stageId,
      priority,
    } = req.query;

    // Build where clause for tickets
    const ticketWhereClause: any = {
      deletedAt: null,
    };

    // Add date filtering
    if (dateFrom || dateTo) {
      ticketWhereClause.createdAt = {};
      if (dateFrom) {
        ticketWhereClause.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        ticketWhereClause.createdAt.lte = new Date(dateTo);
      }
    }

    // Add tag filtering
    if (tags) {
      const tagArray = tags.split(',').map((tag: string) => tag.trim());
      ticketWhereClause.tags = {
        hasSome: tagArray,
      };
    }

    // Add priority filtering
    if (priority) {
      ticketWhereClause.priority = priority;
    }

    // Build where clause for ticket stages
    const stageWhereClause: any = {};
    if (assignedTo) {
      stageWhereClause.assignedTo = assignedTo;
    }

    // Get tickets with stages
    const tickets = await prisma.ticket.findMany({
      where: {
        ...ticketWhereClause,
        ...(Object.keys(stageWhereClause).length > 0 && {
          stages: {
            some: stageWhereClause,
          },
        }),
      },
      include: {
        stages: {
          include: {
            pipelineStage: true,
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
        pipeline: {
          include: {
            stages: {
              orderBy: { order: 'asc' },
            },
          },
        },
      },
    });

    // Get pipeline stages to analyze
    let stagesToAnalyze: any[] = [];
    
    if (stageId) {
      // Analyze specific stage
      const specificStage = await prisma.pipelineStage.findUnique({
        where: { id: stageId },
      });
      if (specificStage) {
        stagesToAnalyze = [specificStage];
      }
    } else {
      // Analyze all stages from tickets
      const allStages = new Map<string, any>();
      tickets.forEach(ticket => {
        if (ticket.pipeline?.stages) {
          ticket.pipeline.stages.forEach(stage => {
            allStages.set(stage.id, stage);
          });
        }
      });
      stagesToAnalyze = Array.from(allStages.values());
    }

    // Analyze time spent in each stage
    const stageTimeAnalysis = stagesToAnalyze.map(stage => {
      const timesInStage: number[] = [];

      tickets.forEach(ticket => {
        if (!ticket.pipeline?.stages) return;

        const ticketStages = ticket.stages.sort((a, b) => 
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );

        const stageEntry = ticketStages.find(ts => ts.pipelineStageId === stage.id);
        if (!stageEntry) return;

        const stageIndex = ticket.pipeline.stages.findIndex(s => s.id === stage.id);
        const nextStageIndex = stageIndex + 1;

        let endTime: Date;

        if (nextStageIndex < ticket.pipeline.stages.length) {
          // Find when ticket moved to next stage
          const nextStageId = ticket.pipeline.stages[nextStageIndex].id;
          const nextStageEntry = ticketStages.find(ts => ts.pipelineStageId === nextStageId);
          
          if (nextStageEntry) {
            endTime = new Date(nextStageEntry.createdAt);
          } else {
            // Ticket hasn't moved to next stage yet
            endTime = new Date(); // Current time
          }
        } else {
          // This is the final stage
          endTime = new Date(); // Current time or could be a completion timestamp
        }

        const timeInStageMs = endTime.getTime() - new Date(stageEntry.createdAt).getTime();
        const timeInStageHours = timeInStageMs / (1000 * 60 * 60);
        
        if (timeInStageHours >= 0) { // Ensure positive time
          timesInStage.push(timeInStageHours);
        }
      });

      // Calculate statistics
      const sampleSize = timesInStage.length;
      let averageTime = 0;
      let medianTime = 0;
      let minTime = 0;
      let maxTime = 0;

      if (sampleSize > 0) {
        // Calculate average
        averageTime = timesInStage.reduce((sum, time) => sum + time, 0) / sampleSize;

        // Calculate median
        const sortedTimes = [...timesInStage].sort((a, b) => a - b);
        const midIndex = Math.floor(sortedTimes.length / 2);
        if (sortedTimes.length % 2 === 0) {
          medianTime = (sortedTimes[midIndex - 1] + sortedTimes[midIndex]) / 2;
        } else {
          medianTime = sortedTimes[midIndex];
        }

        // Min and max
        minTime = Math.min(...timesInStage);
        maxTime = Math.max(...timesInStage);
      }

      return {
        stageId: stage.id,
        stageName: stage.name || 'Unnamed Stage',
        stageOrder: stage.order,
        averageTime: Math.round(averageTime * 100) / 100,
        medianTime: Math.round(medianTime * 100) / 100,
        minTime: Math.round(minTime * 100) / 100,
        maxTime: Math.round(maxTime * 100) / 100,
        timeUnit: "hours",
        sampleSize,
      };
    });

    // Sort by stage order
    stageTimeAnalysis.sort((a, b) => a.stageOrder - b.stageOrder);

    return res.status(200).json({
      success: true,
      data: stageTimeAnalysis,
    });
  } catch (error) {
    console.error("Error in getAverageTimeByStage:", error);
    return handleError(res, error);
  }
};
