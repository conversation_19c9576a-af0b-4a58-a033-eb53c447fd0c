{"version": 3, "file": "generateFilePath.js", "sourceRoot": "", "sources": ["../../src/utils/generateFilePath.ts"], "names": [], "mappings": ";;;AAuCA,gCAwBC;AAGD,wCAQC;AAQD,4CA0GC;AA1LY,QAAA,aAAa,GAAG;IAC3B,WAAW;IACX,QAAQ;IACR,sBAAsB;IACtB,SAAS;IACT,MAAM;IACN,OAAO;IACP,cAAc;IACd,eAAe;CACP,CAAC;AAIX,MAAM,OAAO,GAAG,CAAC,KAAa,EAAwB,EAAE,CACtD,qBAAa,CAAC,QAAQ,CAAC,KAAK,CAAC,WAAW,EAAiB,CAAC,CAAC;AAgB7D,MAAM,eAAe,GAA8B;IACjD,gBAAgB,EAAE,MAAM;IACxB,QAAQ,EAAE,KAAK;IACf,YAAY,EAAE,MAAM;IACpB,YAAY,EAAE,KAAK;CACpB,CAAC;AAEF,SAAgB,UAAU,CACxB,IAAU,EACV,QAAgB;IAEhB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE;QACjD,IAAI,EAAE,SAAS;QACf,KAAK,EAAE,SAAS;QAChB,GAAG,EAAE,SAAS;QACd,QAAQ;KACT,CAAC,CAAC;IAEH,MAAM,KAAK,GAAG,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,CAAC;IAC5C,MAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC;IAC7D,MAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,OAAO,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC;IAClE,MAAM,GAAG,GAAG,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,KAAK,CAAC,EAAE,KAAK,IAAI,EAAE,CAAC;IAE3D,MAAM,OAAO,GAAG,GAAG,IAAI,IAAI,QAAQ,IAAI,GAAG,EAAE,CAAC;IAE7C,MAAM,SAAS,GAAG,IAAI,CAAC,cAAc,CAAC,SAAS,EAAE;QAC/C,KAAK,EAAE,MAAM;QACb,QAAQ;KACT,CAAC,CAAC,WAAW,EAAE,CAAC;IAEjB,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;AAC7C,CAAC;AAGD,SAAgB,cAAc,CAC5B,QAA4B,EAC5B,gBAAwB;IAExB,IAAI,CAAC,QAAQ;QAAE,OAAO,EAAE,CAAC;IACzB,OAAO,QAAQ,CAAC,QAAQ,CAAC,gBAAgB,CAAC;QACxC,CAAC,CAAC,QAAQ;QACV,CAAC,CAAC,GAAG,QAAQ,GAAG,gBAAgB,EAAE,CAAC;AACvC,CAAC;AAED,kDAAkD;AAClD,SAAS,iBAAiB,CAAC,QAA4B;IACrD,IAAI,CAAC,QAAQ;QAAE,OAAO,EAAE,CAAC;IACzB,OAAO,QAAQ,CAAC,OAAO,CAAC,SAAS,EAAE,EAAE,CAAC,CAAC;AACzC,CAAC;AAEM,KAAK,UAAU,gBAAgB,CACpC,GAAgB,EAChB,OAAyB;IAEzB,MAAM,aAAa,GAAG,EAAE,GAAG,eAAe,EAAE,GAAG,OAAO,EAAE,CAAC;IACzD,MAAM,EAAE,YAAY,EAAE,QAAQ,EAAE,gBAAgB,EAAE,YAAY,EAAE,GAC9D,aAAa,CAAC;IAEhB,IAAI,CAAC;QACH,wBAAwB;QACxB,MAAM,MAAM,GAAG,MAAM,YAAY,CAAC,MAAM,CAAC,SAAS,CAAC;YACjD,KAAK,EAAE,EAAE,WAAW,EAAE,GAAG,CAAC,QAAQ,CAAC,EAAE;YACrC,MAAM,EAAE;gBACN,EAAE,EAAE,IAAI;gBACR,WAAW,EAAE,IAAI;gBACjB,SAAS,EAAE;oBACT,MAAM,EAAE;wBACN,IAAI,EAAE,IAAI;qBACX;iBACF;aACF;SACF,CAAC,CAAC;QAEH,yDAAyD;QACzD,MAAM,UAAU,GAAG,MAAM,YAAY,CAAC,uBAAuB,CAAC,UAAU,CAAC;YACvE,KAAK,EAAE,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,EAAE;SAC/B,CAAC,CAAC;QAEH,mBAAmB;QACnB,MAAM,OAAO,GAAG,MAAM,YAAY,CAAC,OAAO,CAAC,SAAS,CAAC;YACnD,KAAK,EAAE,EAAE,IAAI,EAAE,GAAG,CAAC,SAAS,CAAC,EAAE;YAC/B,MAAM,EAAE,EAAE,gBAAgB,EAAC,IAAI,EAAE;SAClC,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,GAAG,CAAC,eAAe,CAAC;YACvC,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACxD,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;QAEf,4BAA4B;QAC5B,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,UAAU,CAAC,YAAY,EAAE,QAAQ,CAAC,CAAC;QAEpE,uDAAuD;QACvD,MAAM,mBAAmB,GAAG,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEjE,qDAAqD;QACrD,MAAM,cAAc,GAAG,iBAAiB,CAAC,GAAG,CAAC,eAAe,CAAC,CAAC,CAAC;QAC/D,MAAM,YAAY,GAAG,mBAAmB;YACtC,CAAC,CAAC,cAAc;YAChB,CAAC,CAAC,cAAc,CAAC,cAAc,EAAE,gBAAgB,CAAC,CAAC;QAErD,6CAA6C;QAC7C,MAAM,cAAc,GAAG,GAAG,CAAC,eAAe,CAAC,IAAI,EAAE,CAAC;QAElD,sDAAsD;QACtD,MAAM,YAAY,GAA2B;YAC3C,SAAS,EAAE,MAAM,CAAC,SAAS,EAAE,IAAI,IAAI,EAAE;YACvC,MAAM,EAAE,MAAM,CAAC,WAAW;YAC1B,OAAO,EAAE,OAAO,CAAC,gBAAgB;YACjC,KAAK,EAAE,KAAK;YACZ,IAAI,EAAE,IAAI;YACV,cAAc,EAAE,cAAc;YAC9B,eAAe,EAAE,YAAY;YAC7B,QAAQ,EAAE,YAAY;YACtB,IAAI,EAAE,OAAO;SACd,CAAC;QAEF,IAAI,UAAU,GAAG,UAAU,CAAC,QAAQ,CAAC;QAErC,4FAA4F;QAC5F,MAAM,gBAAgB,GACpB,mDAAmD,CAAC;QACtD,MAAM,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAC,CAAC,GAAG,CACxE,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,CAChC,CAAC;QAEF,sCAAsC;QACtC,YAAY,CAAC,OAAO,CAAC,CAAC,WAAW,EAAE,EAAE;YACnC,uCAAuC;YACvC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;gBAAE,OAAO;YAElC,MAAM,KAAK,GAAG,YAAY,CAAC,WAAW,CAAC,CAAC;YACxC,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,WAAW,KAAK,cAAc,EAAE,CAAC;oBACnC,MAAM,KAAK,GAAG,6BAA6B,CAAC;oBAC5C,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAChD,CAAC;qBAAM,IAAI,WAAW,KAAK,eAAe,EAAE,CAAC;oBAC3C,MAAM,KAAK,GAAG,IAAI,MAAM,CACtB,MAAM,WAAW,OAAO,WAAW,cAAc,EACjD,GAAG,CACJ,CAAC;oBACF,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,CAAC;gBAChD,CAAC;qBAAM,CAAC;oBACN,MAAM,KAAK,GAAG,IAAI,MAAM,CACtB,MAAM,WAAW,OAAO,WAAW,cAAc,EACjD,GAAG,CACJ,CAAC;oBACF,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,KAAK,EAAE,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;gBAC9D,CAAC;YACH,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO,UAAU,CAAC;IACpB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,OAAO,CAAC,KAAK,CAAC,6BAA6B,EAAE,KAAK,CAAC,CAAC;QACpD,OAAO,EAAE,CAAC;IACZ,CAAC;AACH,CAAC"}