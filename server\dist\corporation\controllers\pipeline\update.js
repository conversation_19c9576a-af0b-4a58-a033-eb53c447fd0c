"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updatePipeline = void 0;
const operation_1 = require("../../../utils/operation");
const updatePipeline = async (req, res) => {
    const id = req.params.id;
    const { corporation_id } = req;
    const { updatedBy, name, description, workType, isActive } = req.body;
    const fields = {
        name: name,
        description: description,
        workType: workType,
        isActive: isActive,
        corporationId: Number(corporation_id),
        updatedBy: updatedBy,
    };
    await (0, operation_1.updateItem)({
        model: "Pipeline",
        fieldName: "id",
        fields: fields,
        id: id,
        res: res,
        req: req,
        successMessage: "Pipeline updated successfully",
    });
};
exports.updatePipeline = updatePipeline;
//# sourceMappingURL=update.js.map