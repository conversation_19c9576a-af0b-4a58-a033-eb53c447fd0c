"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const authentication_1 = require("../../../middleware/authentication");
const create_1 = require("../../controllers/trackSheets/create");
const view_1 = require("../../controllers/trackSheets/view");
const update_1 = require("../../controllers/trackSheets/update");
const delete_1 = require("../../controllers/trackSheets/delete");
const createManifest_1 = require("../../controllers/trackSheets/createManifest");
const viewManifest_1 = require("../../controllers/trackSheets/viewManifest");
const router = (0, express_1.Router)();
router.get("/clients/:id", 
//  authenticate,
view_1.viewTrackSheets);
router.get("/manifest/:id", 
// authenticate,
viewManifest_1.viewManifest);
router.get("/dates", 
// authenticate,
view_1.getRecievedDatesByInvoice);
router.get("/stats", 
// authenticate,
view_1.getInvoiceStats);
router.get("/:id", 
// authenticate,
view_1.ViewTrackSheetsById);
router.post("/", 
// authenticate,
create_1.createTrackSheets);
router.put("/:id", 
// authenticate,
update_1.updateTrackSheets);
router.delete("/:id", authentication_1.authenticate, delete_1.deleteTrackSheets);
router.post("/manifest", 
// authenticate,
createManifest_1.createOrUpdateManifestDetails);
exports.default = router;
//# sourceMappingURL=trackSheetsRoutes.js.map