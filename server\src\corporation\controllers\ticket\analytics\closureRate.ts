import { handleError } from "../../../../utils/helpers";

/**
 * Get closure rate analysis by pipeline stage
 * Shows completion/success rate for each stage
 * 
 * @param req - Express request object with query parameters for filtering
 * @param res - Express response object
 * @returns Closure rate data with entry/completion counts and average times per stage
 */
export const getClosureRateByStage = async (req: any, res: any) => {
  try {
    const {
      dateFrom,
      dateTo,
      tags,
      assignedTo,
      stageId,
      priority,
    } = req.query;

    // Build where clause for tickets
    const ticketWhereClause: any = {
      deletedAt: null,
    };

    // Add date filtering
    if (dateFrom || dateTo) {
      ticketWhereClause.createdAt = {};
      if (dateFrom) {
        ticketWhereClause.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        ticketWhereClause.createdAt.lte = new Date(dateTo);
      }
    }

    // Add tag filtering
    if (tags) {
      const tagArray = tags.split(',').map((tag: string) => tag.trim());
      ticketWhereClause.tags = {
        hasSome: tagArray,
      };
    }

    // Add priority filtering
    if (priority) {
      ticketWhereClause.priority = priority;
    }
    // Build where clause for ticket stages
    const stageWhereClause: any = {};
    if (assignedTo) {
      stageWhereClause.assignedTo = assignedTo;
    }
    if (stageId) {
      stageWhereClause.pipelineStageId = stageId;
    }

    // Get all tickets with their stages and time tracking
    const tickets = await prisma.ticket.findMany({
      where: {
        ...ticketWhereClause,
        ...(Object.keys(stageWhereClause).length > 0 && {
          stages: {
            some: stageWhereClause,
          },
        }),
      },
      include: {
        stages: {
          include: {
            pipelineStage: true,
          },
          orderBy: {
            createdAt: 'asc',
          },
        },
        pipeline: {
          include: {
            stages: {
              orderBy: { order: 'asc' },
            },
          },
        },
      },
    });

    // Get unique pipeline stages
    const allStages = new Map<string, {
      stageId: string;
      stageName: string;
      stageOrder: number;
      pipelineId: string;
    }>();

    tickets.forEach(ticket => {
      if (ticket.pipeline?.stages) {
        ticket.pipeline.stages.forEach(stage => {
          allStages.set(stage.id, {
            stageId: stage.id,
            stageName: stage.name || 'Unnamed Stage',
            stageOrder: stage.order,
            pipelineId: stage.pipelineId || '',
          });
        });
      }
    });

    // Analyze each stage
    const stageAnalysis = Array.from(allStages.values()).map(stageInfo => {
      let ticketsEntered = 0;
      let ticketsCompleted = 0;
      let totalTimeInStage = 0;
      let timeTrackingCount = 0;

      tickets.forEach(ticket => {
        if (!ticket.pipeline?.stages) return;

        const ticketStages = ticket.stages.sort((a, b) => 
          new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
        );

        const stageIndex = ticket.pipeline.stages.findIndex(s => s.id === stageInfo.stageId);
        const hasEnteredStage = ticketStages.some(ts => ts.pipelineStageId === stageInfo.stageId);

        if (hasEnteredStage) {
          ticketsEntered++;

          // Check if ticket completed this stage (moved to next stage or completed pipeline)
          const currentStageEntry = ticketStages.find(ts => ts.pipelineStageId === stageInfo.stageId);
          const nextStageIndex = stageIndex + 1;
          
          if (nextStageIndex < ticket.pipeline.stages.length) {
            // Check if ticket moved to next stage
            const nextStageId = ticket.pipeline.stages[nextStageIndex].id;
            const hasMovedToNext = ticketStages.some(ts => ts.pipelineStageId === nextStageId);
            
            if (hasMovedToNext) {
              ticketsCompleted++;
              
              // Calculate time in stage
              const stageEntry = ticketStages.find(ts => ts.pipelineStageId === stageInfo.stageId);
              const nextStageEntry = ticketStages.find(ts => ts.pipelineStageId === nextStageId);
              
              if (stageEntry && nextStageEntry) {
                const timeInStage = new Date(nextStageEntry.createdAt).getTime() - new Date(stageEntry.createdAt).getTime();
                totalTimeInStage += timeInStage / (1000 * 60 * 60); // Convert to hours
                timeTrackingCount++;
              }
            }
          } else {
            // This is the final stage, check if it's the current stage
            const currentStage = ticketStages[ticketStages.length - 1];
            if (currentStage.pipelineStageId === stageInfo.stageId) {
              ticketsCompleted++;
              
              // Calculate time from stage entry to now (or when it was completed)
              if (currentStageEntry) {
                const now = new Date();
                const timeInStage = now.getTime() - new Date(currentStageEntry.createdAt).getTime();
                totalTimeInStage += timeInStage / (1000 * 60 * 60); // Convert to hours
                timeTrackingCount++;
              }
            }
          }
        }
      });

      const closureRate = ticketsEntered > 0 ? (ticketsCompleted / ticketsEntered) * 100 : 0;
      const averageTimeInStage = timeTrackingCount > 0 ? totalTimeInStage / timeTrackingCount : 0;

      return {
        stageId: stageInfo.stageId,
        stageName: stageInfo.stageName,
        stageOrder: stageInfo.stageOrder,
        ticketsEntered,
        ticketsCompleted,
        closureRate: Math.round(closureRate * 100) / 100,
        averageTimeInStage: Math.round(averageTimeInStage * 100) / 100,
        timeUnit: "hours",
      };
    });

    // Sort by stage order
    stageAnalysis.sort((a, b) => a.stageOrder - b.stageOrder);

    return res.status(200).json({
      success: true,
      data: stageAnalysis,
    });
  } catch (error) {
    console.error("Error in getClosureRateByStage:", error);
    return handleError(res, error);
  }
};
