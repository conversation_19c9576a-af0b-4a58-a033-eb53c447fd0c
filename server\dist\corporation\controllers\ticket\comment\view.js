"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAllComments = exports.getCommentsByTicketId = void 0;
const helpers_1 = require("../../../../utils/helpers");
const getCommentsByTicketId = async (req, res) => {
    try {
        const { ticketId } = req.params;
        if (!ticketId) {
            return res.status(400).json({
                success: false,
                message: "Ticket ID is required",
            });
        }
        const comments = await prisma.comment.findMany({
            where: {
                ticketId,
                deletedAt: null,
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        return res.status(200).json({
            success: true,
            data: comments,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getCommentsByTicketId = getCommentsByTicketId;
const getAllComments = async (req, res) => {
    try {
        const comments = await prisma.comment.findMany({
            where: {
                deletedAt: null,
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        return res.status(200).json({
            success: true,
            data: comments,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getAllComments = getAllComments;
//# sourceMappingURL=view.js.map