"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewPipelineStageById = exports.viewPipelineStage = void 0;
const helpers_1 = require("../../../utils/helpers");
const viewPipelineStage = async (req, res) => {
    try {
        const data = await prisma.pipelineStage.findMany({
            orderBy: {
                createdAt: "desc",
            },
        });
        return res.status(200).json({
            data: data,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewPipelineStage = viewPipelineStage;
const viewPipelineStageById = async (req, res) => {
    try {
        const { id } = req.params;
        if (!id) {
            return res.status(400).json({ error: "Pipeline stage id is required" });
        }
        const data = await prisma.pipelineStage.findUnique({
            where: { id: id },
        });
        if (!data) {
            return res.status(404).json({ error: "Pipeline stage not found" });
        }
        return res.status(200).json({ data });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.viewPipelineStageById = viewPipelineStageById;
//# sourceMappingURL=view.js.map