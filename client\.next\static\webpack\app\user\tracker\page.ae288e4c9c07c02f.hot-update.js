"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/user/tracker/page",{

/***/ "(app-pages-browser)/./app/_component/DataGridTable.tsx":
/*!******************************************!*\
  !*** ./app/_component/DataGridTable.tsx ***!
  \******************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_FaFilter_FaSearch_react_icons_fa__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=FaFilter,FaSearch!=!react-icons/fa */ \"(app-pages-browser)/./node_modules/react-icons/fa/index.mjs\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _Pagination__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./Pagination */ \"(app-pages-browser)/./app/_component/Pagination.tsx\");\n/* harmony import */ var ag_grid_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ag-grid-react */ \"(app-pages-browser)/./node_modules/ag-grid-react/dist/package/index.esm.mjs\");\n/* harmony import */ var ag_grid_community_styles_ag_grid_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ag-grid-community/styles/ag-grid.css */ \"(app-pages-browser)/./node_modules/ag-grid-community/styles/ag-grid.css\");\n/* harmony import */ var ag_grid_community_styles_ag_theme_alpine_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ag-grid-community/styles/ag-theme-alpine.css */ \"(app-pages-browser)/./node_modules/ag-grid-community/styles/ag-theme-alpine.css\");\n/* harmony import */ var ag_grid_community__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ag-grid-community */ \"(app-pages-browser)/./node_modules/ag-grid-community/dist/package/main.esm.mjs\");\n/* harmony import */ var _pms_manage_work_report_WorkReportContext__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../pms/manage_work_report/WorkReportContext */ \"(app-pages-browser)/./app/pms/manage_work_report/WorkReportContext.tsx\");\n/* harmony import */ var _user_tracker_TrackerContext__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../user/tracker/TrackerContext */ \"(app-pages-browser)/./app/user/tracker/TrackerContext.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_ui_customInput__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui/customInput */ \"(app-pages-browser)/./components/ui/customInput.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Register AG Grid modules\nag_grid_community__WEBPACK_IMPORTED_MODULE_13__.ModuleRegistry.registerModules([\n    ag_grid_community__WEBPACK_IMPORTED_MODULE_13__.AllCommunityModule\n]);\nconst DataGridTable = (param)=>{\n    let { columns, data, isLoading, showColDropDowns, filter, filter2, filter3 = false, filter3view, total, totalview, filter_column, filter_column2, filter_column3, filter_column4, totalPages, showPageEntries, className, filter1PlaceHolder, showSearchColumn = true, pageSize, isTimerRunning, setIsTimerRunning, onFilteredDataChange } = param;\n    var _gridRef_current_api, _gridRef_current, _gridRef_current_api1, _gridRef_current1;\n    _s();\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(pageSize || 50);\n    const [selectedColumns, setSelectedColumns] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const params = new URLSearchParams(searchParams);\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname)();\n    const { replace } = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const currentPage = Number(searchParams.get(\"page\")) || 1;\n    const currentPageSize = Number(searchParams.get(\"pageSize\")) || pageSize || 50;\n    const [pagination, setPagination] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        pageIndex: 0,\n        pageSize: page\n    });\n    const { setFromDate, fromDate, toDate, setToDate } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_pms_manage_work_report_WorkReportContext__WEBPACK_IMPORTED_MODULE_9__.WorkReportContext);\n    const { setFDate, fDate, tDate, setTDate } = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(_user_tracker_TrackerContext__WEBPACK_IMPORTED_MODULE_10__.TrackerContext);\n    const [columnState, setColumnState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const gridRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [columnData, setColumnData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [columnVisibility, setColumnVisibility] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [inputValues, setInputValues] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [searchTerms, setSearchTerms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Prepare data with stable serial numbers\n    const processedData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return data === null || data === void 0 ? void 0 : data.map((item, index)=>({\n                ...item,\n                stableId: (currentPage - 1) * currentPageSize + index + 1\n            }));\n    }, [\n        data,\n        currentPage,\n        currentPageSize\n    ]);\n    // Prepare columns with serial number\n    const columnsWithSerialNumber = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        return [\n            {\n                headerName: \"Sr. No.\",\n                field: \"stableId\",\n                sortable: true,\n                width: 80,\n                minWidth: 80,\n                maxWidth: 80,\n                cellStyle: {\n                    textAlign: \"center\"\n                },\n                pinned: \"left\",\n                comparator: (valueA, valueB)=>valueA - valueB\n            },\n            ...columns.filter((col)=>col.field !== \"sr_no\")\n        ];\n    }, [\n        columns\n    ]);\n    // Initialize all columns as visible\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initialVisibility = {};\n        columnsWithSerialNumber.forEach((col)=>{\n            initialVisibility[col.field] = true;\n        });\n        setColumnVisibility(initialVisibility);\n    }, [\n        columnsWithSerialNumber\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        if (columnData !== \"date\") {\n            setFromDate(\"\"), setToDate(\"\");\n            setFDate(\"\"), setTDate(\"\");\n        }\n    }, [\n        columnData\n    ]);\n    // Show or hide overlays based on loading and data state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (gridRef.current && gridRef.current.api) {\n            if (isLoading) {\n                gridRef.current.api.showLoadingOverlay();\n            } else if (!processedData || processedData.length === 0) {\n                gridRef.current.api.showNoRowsOverlay();\n            } else {\n                gridRef.current.api.hideOverlay();\n            }\n        }\n    }, [\n        isLoading,\n        processedData\n    ]);\n    // Handle page change\n    const handlePageChange = (e)=>{\n        const newPageSize = parseInt(e.target.value);\n        setPage(newPageSize);\n        if (totalPages) {\n            params.set(\"pageSize\", newPageSize === null || newPageSize === void 0 ? void 0 : newPageSize.toString());\n            replace(\"\".concat(pathname, \"?\").concat(params.toString()));\n        }\n        setPagination((prevState)=>({\n                ...prevState,\n                pageSize: newPageSize\n            }));\n    };\n    // Handle column selection\n    const handleColumnSelection = (columnKey, columnHeader)=>{\n        setSelectedColumns((prevSelectedColumns)=>{\n            let updatedColumns;\n            if (prevSelectedColumns.includes(columnKey)) {\n                updatedColumns = prevSelectedColumns.filter((col)=>col !== columnKey);\n                const updatedParams = new URLSearchParams(searchParams);\n                if (columnHeader === \"Date\") {\n                    updatedParams.delete(\"fDate\");\n                    updatedParams.delete(\"tDate\");\n                } else {\n                    updatedParams.delete(columnHeader);\n                }\n                replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n            } else {\n                updatedColumns = [\n                    ...prevSelectedColumns,\n                    columnKey\n                ];\n                const updatedParams = new URLSearchParams(searchParams);\n                updatedParams.set(columnHeader, \"\");\n                replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n            }\n            return updatedColumns;\n        });\n    };\n    // Search functionality\n    const handleSearchChange = (event, columnName)=>{\n        const value = event.target.value.trim();\n        if (value) {\n            const updatedParams = new URLSearchParams(searchParams);\n            updatedParams.set(columnName, value);\n            updatedParams.set(\"page\", \"1\");\n            replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n        } else {\n            const updatedParams = new URLSearchParams(searchParams);\n            updatedParams.delete(columnName);\n            replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n        }\n    };\n    const handleKeyDown = (e, columnName)=>{\n        var _inputValues_columnName, _searchTerms_columnName;\n        const currentInput = (_inputValues_columnName = inputValues[columnName]) === null || _inputValues_columnName === void 0 ? void 0 : _inputValues_columnName.trim();\n        if (e.key === \"Enter\") {\n            var _searchTerms_columnName1;\n            e.preventDefault();\n            if (currentInput && !((_searchTerms_columnName1 = searchTerms[columnName]) === null || _searchTerms_columnName1 === void 0 ? void 0 : _searchTerms_columnName1.includes(currentInput))) {\n                const updated = [\n                    ...searchTerms[columnName] || [],\n                    currentInput\n                ];\n                updateSearchParams(columnName, updated);\n                setSearchTerms({\n                    ...searchTerms,\n                    [columnName]: updated\n                });\n                setInputValues({\n                    ...inputValues,\n                    [columnName]: \"\"\n                });\n            }\n        }\n        if (e.key === \"Backspace\" && !currentInput && ((_searchTerms_columnName = searchTerms[columnName]) === null || _searchTerms_columnName === void 0 ? void 0 : _searchTerms_columnName.length) > 0) {\n            const updated = [\n                ...searchTerms[columnName]\n            ];\n            updated.pop();\n            updateSearchParams(columnName, updated);\n            setSearchTerms({\n                ...searchTerms,\n                [columnName]: updated\n            });\n        }\n    };\n    const handleRemoveTerm = (columnName, term)=>{\n        const updated = (searchTerms[columnName] || []).filter((t)=>t !== term);\n        updateSearchParams(columnName, updated);\n        setSearchTerms({\n            ...searchTerms,\n            [columnName]: updated\n        });\n    };\n    const updateSearchParams = (columnName, terms)=>{\n        const updatedParams = new URLSearchParams(searchParams);\n        if (terms.length > 0) {\n            updatedParams.set(columnName, terms.join(\",\"));\n            updatedParams.set(\"page\", \"1\");\n        } else {\n            updatedParams.delete(columnName);\n        }\n        replace(\"\".concat(pathname, \"?\").concat(updatedParams.toString()));\n    };\n    // Filter columns based on selected ones\n    const filterColumns = selectedColumns.length ? selectedColumns.map((columnKey)=>columnsWithSerialNumber.find((col)=>col.field === columnKey)) : [];\n    const toggleColumnVisibility = (field, isVisible)=>{\n        setColumnVisibility((prev)=>({\n                ...prev,\n                [field]: isVisible\n            }));\n        if (gridRef.current) {\n            gridRef.current.api.setColumnsVisible([\n                field\n            ], isVisible);\n        }\n    };\n    // Add a custom message for no data available\n    const noRowsOverlayTemplate = '<span class=\"ag-overlay-no-rows-center\">No Data Available</span>';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"animate-in fade-in duration-1000\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-between w-full lg:w-full lg:flex lg:justify-end mb-3 animate-in fade-in duration-1000 gap-5\",\n                children: [\n                    showPageEntries && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                        value: page,\n                        onChange: handlePageChange,\n                        className: \" border-2 rounded-md  items-center justify-center cursor-pointer\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 10,\n                                children: \"10\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 15,\n                                children: \"15\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 306,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 25,\n                                children: \"25\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 50,\n                                children: \"50\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 100,\n                                children: \"100\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 309,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 250,\n                                children: \"250\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 500,\n                                children: \"500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 311,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 1000,\n                                children: \"1000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 1500,\n                                children: \"1500\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                value: 2000,\n                                children: \"2000\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 314,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 11\n                    }, undefined),\n                    showColDropDowns && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"p-2 border-2 rounded-md flex items-center justify-center px-3 cursor-pointer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaFilter_FaSearch_react_icons_fa__WEBPACK_IMPORTED_MODULE_14__.FaFilter, {}, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                        lineNumber: 322,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                    lineNumber: 321,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 320,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                className: \"bg-white border-none dark:bg-gray-900 dark:ring-1 dark:ring-gray-800\",\n                                onSelect: (e)=>e.preventDefault(),\n                                children: columnsWithSerialNumber.filter((column)=>column.hideable !== false).map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuCheckboxItem, {\n                                        className: \"capitalize cursor-pointer\",\n                                        checked: columnVisibility[column.field],\n                                        onCheckedChange: (value)=>toggleColumnVisibility(column.field, value),\n                                        onSelect: (e)=>e.preventDefault(),\n                                        children: column.headerName || column.field\n                                    }, column.field, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 325,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 11\n                    }, undefined),\n                    showSearchColumn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenu, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"p-2 border-2 rounded-md flex items-center justify-center px-3 cursor-pointer\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_FaFilter_FaSearch_react_icons_fa__WEBPACK_IMPORTED_MODULE_14__.FaSearch, {}, void 0, false, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                    lineNumber: 351,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 350,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuContent, {\n                                className: \"bg-white border-none dark:bg-gray-900 dark:ring-1 dark:ring-gray-800\",\n                                onSelect: (e)=>e.preventDefault(),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"px-3 py-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold text-sm\",\n                                            children: \"Select Columns\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                            lineNumber: 360,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        columnsWithSerialNumber.filter((item)=>item.field !== \"action\" && item.field !== \"Activity Log\" && item.field !== \"pause\" && item.field !== \"finish_time\" && item.field !== \"start_time\" && item.field !== \"time_spent\" && item.field !== \"sr_no\" && item.field !== \"payment terms\" && item.field !== \"stableId\").map((item, id)=>{\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.DropdownMenuCheckboxItem, {\n                                                checked: selectedColumns.includes(item.field),\n                                                onCheckedChange: ()=>handleColumnSelection(item.field, item.headerName),\n                                                className: \"capitalize cursor-pointer\",\n                                                onSelect: (e)=>e.preventDefault(),\n                                                children: item.headerName\n                                            }, id, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                                lineNumber: 376,\n                                                columnNumber: 23\n                                            }, undefined);\n                                        })\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                    lineNumber: 359,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 355,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 11\n                    }, undefined),\n                    filterColumns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: filterColumns.map((col, index)=>{\n                            var _searchTerms_col_headerName, _searchTerms_col_headerName1;\n                            return (col === null || col === void 0 ? void 0 : col.field) === \"date\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full md:w-auto\",\n                                children: filter3view\n                            }, index, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 398,\n                                columnNumber: 17\n                            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex- w-full md:w-[calc(20%-1rem)] min-w-[100px]\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap items-center gap-1 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md  px-1 focus-within:ring-0 focus-within:ring-blue-600 focus-within:border-blue-600 shadow-sm\",\n                                        children: [\n                                            ((_searchTerms_col_headerName = searchTerms[col.headerName]) === null || _searchTerms_col_headerName === void 0 ? void 0 : _searchTerms_col_headerName.length) > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                                children: searchTerms[col.headerName].map((term, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \" flex items-center bg-gray-100 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-2 rounded-full text-xs\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"mr-1 truncate max-w-xs\",\n                                                                children: term\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                                                lineNumber: 416,\n                                                                columnNumber: 31\n                                                            }, undefined),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: (e)=>{\n                                                                    e.preventDefault();\n                                                                    handleRemoveTerm(col.headerName, term);\n                                                                },\n                                                                className: \"  text-gray-500 dark:text-gray-300 hover:text-gray-700 dark:hover:text-gray-100\",\n                                                                children: \"\\xd7\"\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                                                lineNumber: 419,\n                                                                columnNumber: 31\n                                                            }, undefined)\n                                                        ]\n                                                    }, i, true, {\n                                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 29\n                                                    }, undefined))\n                                            }, void 0, false),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_customInput__WEBPACK_IMPORTED_MODULE_12__.CustomInput, {\n                                                value: inputValues[col.headerName] || \"\",\n                                                onChange: (e)=>setInputValues({\n                                                        ...inputValues,\n                                                        [col.headerName]: e.target.value\n                                                    }),\n                                                onKeyDown: (e)=>handleKeyDown(e, col.headerName),\n                                                placeholder: ((_searchTerms_col_headerName1 = searchTerms[col.headerName]) === null || _searchTerms_col_headerName1 === void 0 ? void 0 : _searchTerms_col_headerName1.length) > 0 ? \"\" : \"Search \".concat(col.headerName, \"...\"),\n                                                className: \"flex-1 min-w-[30px] bg-transparent border-none focus:outline-none\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                                lineNumber: 432,\n                                                columnNumber: 23\n                                            }, undefined)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                        lineNumber: 407,\n                                        columnNumber: 21\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                    lineNumber: 406,\n                                    columnNumber: 19\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 402,\n                                columnNumber: 17\n                            }, undefined);\n                        })\n                    }, void 0, false),\n                    filter && filterColumns.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: filterColumns.map((column, index)=>{\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: filter1PlaceHolder ? filter1PlaceHolder : filter_column,\n                                value: inputValues[column.headerName] || \"\",\n                                onChange: (e)=>setInputValues({\n                                        ...inputValues,\n                                        [column.headerName]: e.target.value\n                                    }),\n                                onKeyDown: (e)=>handleKeyDown(e, column.headerName),\n                                className: \"w-[20%] dark:bg-gray-700 !outline-main-color\"\n                            }, index, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false),\n                    filter2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: filterColumns.map((column, index)=>{\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: \"\".concat(filter_column2),\n                                value: inputValues[column.headerName] || \"\",\n                                onChange: (e)=>setInputValues({\n                                        ...inputValues,\n                                        [column.headerName]: e.target.value\n                                    }),\n                                onKeyDown: (e)=>handleKeyDown(e, column.headerName),\n                                className: \"w-[20%] dark:bg-gray-700 !outline-main-color\"\n                            }, index, false, {\n                                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                                lineNumber: 479,\n                                columnNumber: 15\n                            }, undefined);\n                        })\n                    }, void 0, false)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                lineNumber: 298,\n                columnNumber: 7\n            }, undefined),\n            total && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: totalview\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                lineNumber: 497,\n                columnNumber: 17\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_11__.cn)(\"\", className),\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"ag-theme-alpine custom-ag-grid\",\n                    style: {\n                        height: \"100vh\",\n                        width: \"100%\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ag_grid_react__WEBPACK_IMPORTED_MODULE_6__.AgGridReact, {\n                        ref: gridRef,\n                        rowData: processedData,\n                        columnDefs: columnsWithSerialNumber,\n                        headerHeight: 35,\n                        overlayNoRowsTemplate: noRowsOverlayTemplate,\n                        onGridReady: (params)=>{\n                            params.api.sizeColumnsToFit();\n                            // Show overlays on grid ready\n                            if (isLoading) {\n                                params.api.showLoadingOverlay();\n                            } else if (!processedData || processedData.length === 0) {\n                                params.api.showNoRowsOverlay();\n                            } else {\n                                params.api.hideOverlay();\n                            }\n                        },\n                        onFirstDataRendered: (params)=>{\n                            params.api.sizeColumnsToFit();\n                        },\n                        onColumnVisible: (event)=>{\n                            event.api.sizeColumnsToFit();\n                        },\n                        onGridSizeChanged: (params)=>{\n                            params.api.sizeColumnsToFit();\n                        },\n                        defaultColDef: {\n                            sortable: true,\n                            resizable: true,\n                            cellStyle: {\n                                borderRight: \"1px solid #ddd\"\n                            }\n                        }\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                        lineNumber: 504,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                    lineNumber: 500,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                lineNumber: 499,\n                columnNumber: 7\n            }, undefined),\n            data && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_Pagination__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                currentPage: totalPages ? Number(params.get(\"page\")) || 1 : (((_gridRef_current = gridRef.current) === null || _gridRef_current === void 0 ? void 0 : (_gridRef_current_api = _gridRef_current.api) === null || _gridRef_current_api === void 0 ? void 0 : _gridRef_current_api.paginationGetCurrentPage()) || 0) + 1,\n                totalPages: totalPages ? totalPages : ((_gridRef_current1 = gridRef.current) === null || _gridRef_current1 === void 0 ? void 0 : (_gridRef_current_api1 = _gridRef_current1.api) === null || _gridRef_current_api1 === void 0 ? void 0 : _gridRef_current_api1.paginationGetTotalPages()) || 1,\n                onPageChange: (page)=>{\n                    if (gridRef.current) {\n                        var _gridRef_current_api, _gridRef_current;\n                        gridRef === null || gridRef === void 0 ? void 0 : (_gridRef_current = gridRef.current) === null || _gridRef_current === void 0 ? void 0 : (_gridRef_current_api = _gridRef_current.api) === null || _gridRef_current_api === void 0 ? void 0 : _gridRef_current_api.paginationGoToPage(page - 1);\n                    }\n                    if (totalPages) {\n                        params.set(\"page\", page.toString());\n                        replace(\"\".concat(pathname, \"?\").concat(params.toString()));\n                    }\n                }\n            }, void 0, false, {\n                fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n                lineNumber: 540,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\_component\\\\DataGridTable.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, undefined);\n};\n_s(DataGridTable, \"QhHt7ZuUi02iL7ajDvXq754ITGE=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.usePathname,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = DataGridTable;\n/* harmony default export */ __webpack_exports__[\"default\"] = (DataGridTable);\nvar _c;\n$RefreshReg$(_c, \"DataGridTable\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9fY29tcG9uZW50L0RhdGFHcmlkVGFibGUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFDZ0Y7QUFDNUI7QUFDc0I7QUFNbkM7QUFDTztBQUNSO0FBQ007QUFDRTtBQUNRO0FBQ2lCO0FBQ1M7QUFDaEI7QUFDL0I7QUFDeUI7QUFFMUQsMkJBQTJCO0FBQzNCbUIsOERBQWNBLENBQUNLLGVBQWUsQ0FBQztJQUFDTixrRUFBa0JBO0NBQUM7QUE2Qm5ELE1BQU1PLGdCQUFnQjtRQUFDLEVBQ3JCQyxPQUFPLEVBQ1BDLElBQUksRUFDSkMsU0FBUyxFQUNUQyxnQkFBZ0IsRUFDaEJDLE1BQU0sRUFDTkMsT0FBTyxFQUNQQyxVQUFVLEtBQUssRUFDZkMsV0FBVyxFQUNYQyxLQUFLLEVBQ0xDLFNBQVMsRUFDVEMsYUFBYSxFQUNiQyxjQUFjLEVBQ2RDLGNBQWMsRUFDZEMsY0FBYyxFQUNkQyxVQUFVLEVBQ1ZDLGVBQWUsRUFDZkMsU0FBUyxFQUNUQyxrQkFBa0IsRUFDbEJDLG1CQUFtQixJQUFJLEVBQ3ZCQyxRQUFRLEVBQ1JDLGNBQWMsRUFDZEMsaUJBQWlCLEVBQ2pCQyxvQkFBb0IsRUFDRDtRQW9kSkMsc0JBQUFBLGtCQUtEQSx1QkFBQUE7O0lBeGRkLE1BQU0sQ0FBQ0MsTUFBTUMsUUFBUSxHQUFHbEQsK0NBQVFBLENBQVM0QyxZQUFZO0lBQ3JELE1BQU0sQ0FBQ08saUJBQWlCQyxtQkFBbUIsR0FBR3BELCtDQUFRQSxDQUFXLEVBQUU7SUFDbkUsTUFBTXFELGVBQWU3QyxnRUFBZUE7SUFDcEMsTUFBTThDLFNBQVMsSUFBSUMsZ0JBQWdCRjtJQUNuQyxNQUFNRyxXQUFXL0MsNERBQVdBO0lBQzVCLE1BQU0sRUFBRWdELE9BQU8sRUFBRSxHQUFHbEQsMERBQVNBO0lBRTdCLE1BQU1tRCxjQUFjQyxPQUFPTixhQUFhTyxHQUFHLENBQUMsWUFBWTtJQUN4RCxNQUFNQyxrQkFDSkYsT0FBT04sYUFBYU8sR0FBRyxDQUFDLGdCQUFnQmhCLFlBQVk7SUFFdEQsTUFBTSxDQUFDa0IsWUFBWUMsY0FBYyxHQUFHL0QsK0NBQVFBLENBQU07UUFDaERnRSxXQUFXO1FBQ1hwQixVQUFVSztJQUNaO0lBRUEsTUFBTSxFQUFFZ0IsV0FBVyxFQUFFQyxRQUFRLEVBQUVDLE1BQU0sRUFBRUMsU0FBUyxFQUFFLEdBQ2hEakUsaURBQVVBLENBQUNnQix3RkFBaUJBO0lBQzlCLE1BQU0sRUFBRWtELFFBQVEsRUFBRUMsS0FBSyxFQUFFQyxLQUFLLEVBQUVDLFFBQVEsRUFBRSxHQUFHckUsaURBQVVBLENBQUNpQix5RUFBY0E7SUFFdEUsTUFBTSxDQUFDcUQsYUFBYUMsZUFBZSxHQUFHMUUsK0NBQVFBLENBQVEsRUFBRTtJQUN4RCxNQUFNZ0QsVUFBVTlDLDZDQUFNQSxDQUFjO0lBRXBDLE1BQU0sQ0FBQ3lFLFlBQVlDLGNBQWMsR0FBRzVFLCtDQUFRQSxDQUFDO0lBQzdDLE1BQU0sQ0FBQzZFLGtCQUFrQkMsb0JBQW9CLEdBQUc5RSwrQ0FBUUEsQ0FBTSxDQUFDO0lBRS9ELE1BQU0sQ0FBQytFLGFBQWFDLGVBQWUsR0FBR2hGLCtDQUFRQSxDQUE0QixDQUFDO0lBRTNFLE1BQU0sQ0FBQ2lGLGFBQWFDLGVBQWUsR0FBR2xGLCtDQUFRQSxDQUM1QyxDQUFDO0lBR0gsMENBQTBDO0lBQzFDLE1BQU1tRixnQkFBZ0IvRSw4Q0FBT0EsQ0FBQztRQUM1QixPQUFPc0IsaUJBQUFBLDJCQUFBQSxLQUFNMEQsR0FBRyxDQUFDLENBQUNDLE1BQU1DLFFBQVc7Z0JBQ2pDLEdBQUdELElBQUk7Z0JBQ1BFLFVBQVUsQ0FBQzdCLGNBQWMsS0FBS0csa0JBQWtCeUIsUUFBUTtZQUMxRDtJQUNGLEdBQUc7UUFBQzVEO1FBQU1nQztRQUFhRztLQUFnQjtJQUV2QyxxQ0FBcUM7SUFDckMsTUFBTTJCLDBCQUEwQnBGLDhDQUFPQSxDQUFDO1FBQ3RDLE9BQU87WUFDTDtnQkFDRXFGLFlBQVk7Z0JBQ1pDLE9BQU87Z0JBQ1BDLFVBQVU7Z0JBQ1ZDLE9BQU87Z0JBQ1BDLFVBQVU7Z0JBQ1ZDLFVBQVU7Z0JBQ1ZDLFdBQVc7b0JBQ1RDLFdBQVc7Z0JBQ2I7Z0JBQ0FDLFFBQVE7Z0JBRVJDLFlBQVksQ0FBQ0MsUUFBZ0JDLFNBQW1CRCxTQUFTQztZQUMzRDtlQUNHM0UsUUFBUUksTUFBTSxDQUFDLENBQUN3RSxNQUFRQSxJQUFJWCxLQUFLLEtBQUs7U0FDMUM7SUFDSCxHQUFHO1FBQUNqRTtLQUFRO0lBRVosb0NBQW9DO0lBQ3BDeEIsZ0RBQVNBLENBQUM7UUFDUixNQUFNcUcsb0JBQXlCLENBQUM7UUFDaENkLHdCQUF3QmUsT0FBTyxDQUFDLENBQUNGO1lBQy9CQyxpQkFBaUIsQ0FBQ0QsSUFBSVgsS0FBSyxDQUFDLEdBQUc7UUFDakM7UUFDQVosb0JBQW9Cd0I7SUFDdEIsR0FBRztRQUFDZDtLQUF3QjtJQUU1QnZGLGdEQUFTQSxDQUFDO1FBQ1IsSUFBSTBFLGVBQWUsUUFBUTtZQUN6QlYsWUFBWSxLQUFLRyxVQUFVO1lBQzNCQyxTQUFTLEtBQUtHLFNBQVM7UUFDekI7SUFDRixHQUFHO1FBQUNHO0tBQVc7SUFFZix3REFBd0Q7SUFDeER2RSw4Q0FBT0EsQ0FBQztRQUNOLElBQUk0QyxRQUFRd0QsT0FBTyxJQUFJeEQsUUFBUXdELE9BQU8sQ0FBQ0MsR0FBRyxFQUFFO1lBQzFDLElBQUk5RSxXQUFXO2dCQUNicUIsUUFBUXdELE9BQU8sQ0FBQ0MsR0FBRyxDQUFDQyxrQkFBa0I7WUFDeEMsT0FBTyxJQUFJLENBQUN2QixpQkFBaUJBLGNBQWN3QixNQUFNLEtBQUssR0FBRztnQkFDdkQzRCxRQUFRd0QsT0FBTyxDQUFDQyxHQUFHLENBQUNHLGlCQUFpQjtZQUN2QyxPQUFPO2dCQUNMNUQsUUFBUXdELE9BQU8sQ0FBQ0MsR0FBRyxDQUFDSSxXQUFXO1lBQ2pDO1FBQ0Y7SUFDRixHQUFHO1FBQUNsRjtRQUFXd0Q7S0FBYztJQUU3QixxQkFBcUI7SUFDckIsTUFBTTJCLG1CQUFtQixDQUFDQztRQUN4QixNQUFNQyxjQUFjQyxTQUFTRixFQUFFRyxNQUFNLENBQUNDLEtBQUs7UUFDM0NqRSxRQUFROEQ7UUFDUixJQUFJekUsWUFBWTtZQUNkZSxPQUFPOEQsR0FBRyxDQUFDLFlBQVlKLHdCQUFBQSxrQ0FBQUEsWUFBYUssUUFBUTtZQUM1QzVELFFBQVEsR0FBZUgsT0FBWkUsVUFBUyxLQUFxQixPQUFsQkYsT0FBTytELFFBQVE7UUFDeEM7UUFDQXRELGNBQWMsQ0FBQ3VELFlBQWU7Z0JBQzVCLEdBQUdBLFNBQVM7Z0JBQ1oxRSxVQUFVb0U7WUFDWjtJQUNGO0lBRUEsMEJBQTBCO0lBQzFCLE1BQU1PLHdCQUF3QixDQUFDQyxXQUFtQkM7UUFDaERyRSxtQkFBbUIsQ0FBQ3NFO1lBQ2xCLElBQUlDO1lBRUosSUFBSUQsb0JBQW9CRSxRQUFRLENBQUNKLFlBQVk7Z0JBQzNDRyxpQkFBaUJELG9CQUFvQjdGLE1BQU0sQ0FBQyxDQUFDd0UsTUFBUUEsUUFBUW1CO2dCQUU3RCxNQUFNSyxnQkFBZ0IsSUFBSXRFLGdCQUFnQkY7Z0JBRTFDLElBQUlvRSxpQkFBaUIsUUFBUTtvQkFDM0JJLGNBQWNDLE1BQU0sQ0FBQztvQkFDckJELGNBQWNDLE1BQU0sQ0FBQztnQkFDdkIsT0FBTztvQkFDTEQsY0FBY0MsTUFBTSxDQUFDTDtnQkFDdkI7Z0JBRUFoRSxRQUFRLEdBQWVvRSxPQUFackUsVUFBUyxLQUE0QixPQUF6QnFFLGNBQWNSLFFBQVE7WUFDL0MsT0FBTztnQkFDTE0saUJBQWlCO3VCQUFJRDtvQkFBcUJGO2lCQUFVO2dCQUVwRCxNQUFNSyxnQkFBZ0IsSUFBSXRFLGdCQUFnQkY7Z0JBQzFDd0UsY0FBY1QsR0FBRyxDQUFDSyxjQUFjO2dCQUNoQ2hFLFFBQVEsR0FBZW9FLE9BQVpyRSxVQUFTLEtBQTRCLE9BQXpCcUUsY0FBY1IsUUFBUTtZQUMvQztZQUVBLE9BQU9NO1FBQ1Q7SUFDRjtJQUVBLHVCQUF1QjtJQUN2QixNQUFNSSxxQkFBcUIsQ0FDekJDLE9BQ0FDO1FBRUEsTUFBTWQsUUFBUWEsTUFBTWQsTUFBTSxDQUFDQyxLQUFLLENBQUNlLElBQUk7UUFFckMsSUFBSWYsT0FBTztZQUNULE1BQU1VLGdCQUFnQixJQUFJdEUsZ0JBQWdCRjtZQUMxQ3dFLGNBQWNULEdBQUcsQ0FBQ2EsWUFBWWQ7WUFDOUJVLGNBQWNULEdBQUcsQ0FBQyxRQUFRO1lBQzFCM0QsUUFBUSxHQUFlb0UsT0FBWnJFLFVBQVMsS0FBNEIsT0FBekJxRSxjQUFjUixRQUFRO1FBQy9DLE9BQU87WUFDTCxNQUFNUSxnQkFBZ0IsSUFBSXRFLGdCQUFnQkY7WUFDMUN3RSxjQUFjQyxNQUFNLENBQUNHO1lBQ3JCeEUsUUFBUSxHQUFlb0UsT0FBWnJFLFVBQVMsS0FBNEIsT0FBekJxRSxjQUFjUixRQUFRO1FBQy9DO0lBQ0Y7SUFFQSxNQUFNYyxnQkFBZ0IsQ0FDcEJwQixHQUNBa0I7WUFFcUJsRCx5QkFlbkJFO1FBZkYsTUFBTW1ELGdCQUFlckQsMEJBQUFBLFdBQVcsQ0FBQ2tELFdBQVcsY0FBdkJsRCw4Q0FBQUEsd0JBQXlCbUQsSUFBSTtRQUVsRCxJQUFJbkIsRUFBRXNCLEdBQUcsS0FBSyxTQUFTO2dCQUVBcEQ7WUFEckI4QixFQUFFdUIsY0FBYztZQUNoQixJQUFJRixnQkFBZ0IsR0FBQ25ELDJCQUFBQSxXQUFXLENBQUNnRCxXQUFXLGNBQXZCaEQsK0NBQUFBLHlCQUF5QjJDLFFBQVEsQ0FBQ1EsZ0JBQWU7Z0JBQ3BFLE1BQU1HLFVBQVU7dUJBQUt0RCxXQUFXLENBQUNnRCxXQUFXLElBQUksRUFBRTtvQkFBR0c7aUJBQWE7Z0JBQ2xFSSxtQkFBbUJQLFlBQVlNO2dCQUMvQnJELGVBQWU7b0JBQUUsR0FBR0QsV0FBVztvQkFBRSxDQUFDZ0QsV0FBVyxFQUFFTTtnQkFBUTtnQkFDdkR2RCxlQUFlO29CQUFFLEdBQUdELFdBQVc7b0JBQUUsQ0FBQ2tELFdBQVcsRUFBRTtnQkFBRztZQUNwRDtRQUNGO1FBRUEsSUFDRWxCLEVBQUVzQixHQUFHLEtBQUssZUFDVixDQUFDRCxnQkFDRG5ELEVBQUFBLDBCQUFBQSxXQUFXLENBQUNnRCxXQUFXLGNBQXZCaEQsOENBQUFBLHdCQUF5QjBCLE1BQU0sSUFBRyxHQUNsQztZQUNBLE1BQU00QixVQUFVO21CQUFJdEQsV0FBVyxDQUFDZ0QsV0FBVzthQUFDO1lBQzVDTSxRQUFRRSxHQUFHO1lBQ1hELG1CQUFtQlAsWUFBWU07WUFDL0JyRCxlQUFlO2dCQUFFLEdBQUdELFdBQVc7Z0JBQUUsQ0FBQ2dELFdBQVcsRUFBRU07WUFBUTtRQUN6RDtJQUNGO0lBRUEsTUFBTUcsbUJBQW1CLENBQUNULFlBQW9CVTtRQUM1QyxNQUFNSixVQUFVLENBQUN0RCxXQUFXLENBQUNnRCxXQUFXLElBQUksRUFBRSxFQUFFcEcsTUFBTSxDQUFDLENBQUMrRyxJQUFNQSxNQUFNRDtRQUNwRUgsbUJBQW1CUCxZQUFZTTtRQUMvQnJELGVBQWU7WUFBRSxHQUFHRCxXQUFXO1lBQUUsQ0FBQ2dELFdBQVcsRUFBRU07UUFBUTtJQUN6RDtJQUVBLE1BQU1DLHFCQUFxQixDQUFDUCxZQUFvQlk7UUFDOUMsTUFBTWhCLGdCQUFnQixJQUFJdEUsZ0JBQWdCRjtRQUMxQyxJQUFJd0YsTUFBTWxDLE1BQU0sR0FBRyxHQUFHO1lBQ3BCa0IsY0FBY1QsR0FBRyxDQUFDYSxZQUFZWSxNQUFNQyxJQUFJLENBQUM7WUFDekNqQixjQUFjVCxHQUFHLENBQUMsUUFBUTtRQUM1QixPQUFPO1lBQ0xTLGNBQWNDLE1BQU0sQ0FBQ0c7UUFDdkI7UUFDQXhFLFFBQVEsR0FBZW9FLE9BQVpyRSxVQUFTLEtBQTRCLE9BQXpCcUUsY0FBY1IsUUFBUTtJQUMvQztJQUVBLHdDQUF3QztJQUN4QyxNQUFNMEIsZ0JBQWdCNUYsZ0JBQWdCd0QsTUFBTSxHQUN4Q3hELGdCQUFnQmlDLEdBQUcsQ0FBQyxDQUFDb0MsWUFDbkJoQyx3QkFBd0J3RCxJQUFJLENBQUMsQ0FBQzNDLE1BQVFBLElBQUlYLEtBQUssS0FBSzhCLGNBRXRELEVBQUU7SUFFTixNQUFNeUIseUJBQXlCLENBQUN2RCxPQUFld0Q7UUFDN0NwRSxvQkFBb0IsQ0FBQ3FFLE9BQVU7Z0JBQzdCLEdBQUdBLElBQUk7Z0JBQ1AsQ0FBQ3pELE1BQU0sRUFBRXdEO1lBQ1g7UUFFQSxJQUFJbEcsUUFBUXdELE9BQU8sRUFBRTtZQUNuQnhELFFBQVF3RCxPQUFPLENBQUNDLEdBQUcsQ0FBQzJDLGlCQUFpQixDQUFDO2dCQUFDMUQ7YUFBTSxFQUFFd0Q7UUFDakQ7SUFDRjtJQUVBLDZDQUE2QztJQUM3QyxNQUFNRyx3QkFBd0I7SUFFOUIscUJBQ0UsOERBQUNDO1FBQUk3RyxXQUFZOzswQkFDZiw4REFBQzZHO2dCQUFJN0csV0FBVTs7b0JBQ1pELGlDQUNDLDhEQUFDK0c7d0JBQ0NwQyxPQUFPbEU7d0JBQ1B1RyxVQUFVMUM7d0JBQ1ZyRSxXQUFVOzswQ0FFViw4REFBQ2dIO2dDQUFPdEMsT0FBTzswQ0FBSTs7Ozs7OzBDQUNuQiw4REFBQ3NDO2dDQUFPdEMsT0FBTzswQ0FBSTs7Ozs7OzBDQUNuQiw4REFBQ3NDO2dDQUFPdEMsT0FBTzswQ0FBSTs7Ozs7OzBDQUNuQiw4REFBQ3NDO2dDQUFPdEMsT0FBTzswQ0FBSTs7Ozs7OzBDQUNuQiw4REFBQ3NDO2dDQUFPdEMsT0FBTzswQ0FBSzs7Ozs7OzBDQUNwQiw4REFBQ3NDO2dDQUFPdEMsT0FBTzswQ0FBSzs7Ozs7OzBDQUNwQiw4REFBQ3NDO2dDQUFPdEMsT0FBTzswQ0FBSzs7Ozs7OzBDQUNwQiw4REFBQ3NDO2dDQUFPdEMsT0FBTzswQ0FBTTs7Ozs7OzBDQUNyQiw4REFBQ3NDO2dDQUFPdEMsT0FBTzswQ0FBTTs7Ozs7OzBDQUNyQiw4REFBQ3NDO2dDQUFPdEMsT0FBTzswQ0FBTTs7Ozs7Ozs7Ozs7O29CQUl4QnZGLGtDQUNDLDhEQUFDbEIsc0VBQVlBOzswQ0FDWCw4REFBQ0csNkVBQW1CQTtnQ0FBQzZJLE9BQU87MENBQzFCLDRFQUFDQztvQ0FBRWxILFdBQVU7OENBQ1gsNEVBQUNwQyw4RkFBUUE7Ozs7Ozs7Ozs7Ozs7OzswQ0FHYiw4REFBQ08sNkVBQW1CQTtnQ0FDbEI2QixXQUFVO2dDQUNWbUgsVUFBVSxDQUFDN0MsSUFBTUEsRUFBRXVCLGNBQWM7MENBRWhDOUMsd0JBQ0UzRCxNQUFNLENBQUMsQ0FBQ2dJLFNBQVdBLE9BQU9DLFFBQVEsS0FBSyxPQUN2QzFFLEdBQUcsQ0FBQyxDQUFDeUUsdUJBQ0osOERBQUNsSixrRkFBd0JBO3dDQUV2QjhCLFdBQVU7d0NBQ1ZzSCxTQUFTbEYsZ0JBQWdCLENBQUNnRixPQUFPbkUsS0FBSyxDQUFDO3dDQUN2Q3NFLGlCQUFpQixDQUFDN0MsUUFDaEI4Qix1QkFBdUJZLE9BQU9uRSxLQUFLLEVBQUV5Qjt3Q0FFdkN5QyxVQUFVLENBQUM3QyxJQUFNQSxFQUFFdUIsY0FBYztrREFFaEN1QixPQUFPcEUsVUFBVSxJQUFJb0UsT0FBT25FLEtBQUs7dUNBUjdCbUUsT0FBT25FLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7b0JBZTVCL0Msa0NBQ0MsOERBQUNqQyxzRUFBWUE7OzBDQUNYLDhEQUFDRyw2RUFBbUJBO2dDQUFDNkksT0FBTzswQ0FDMUIsNEVBQUNDO29DQUFFbEgsV0FBVTs4Q0FDWCw0RUFBQ25DLDhGQUFRQTs7Ozs7Ozs7Ozs7Ozs7OzBDQUdiLDhEQUFDTSw2RUFBbUJBO2dDQUNsQjZCLFdBQVU7Z0NBQ1ZtSCxVQUFVLENBQUM3QyxJQUFNQSxFQUFFdUIsY0FBYzswQ0FFakMsNEVBQUNnQjtvQ0FBSTdHLFdBQVU7O3NEQUNiLDhEQUFDa0g7NENBQUVsSCxXQUFVO3NEQUF3Qjs7Ozs7O3dDQUNwQytDLHdCQUNFM0QsTUFBTSxDQUNMLENBQUN3RCxPQUNDQSxLQUFLSyxLQUFLLEtBQUssWUFDZkwsS0FBS0ssS0FBSyxLQUFLLGtCQUNmTCxLQUFLSyxLQUFLLEtBQUssV0FDZkwsS0FBS0ssS0FBSyxLQUFLLGlCQUNmTCxLQUFLSyxLQUFLLEtBQUssZ0JBQ2ZMLEtBQUtLLEtBQUssS0FBSyxnQkFDZkwsS0FBS0ssS0FBSyxLQUFLLFdBQ2ZMLEtBQUtLLEtBQUssS0FBSyxtQkFDZkwsS0FBS0ssS0FBSyxLQUFLLFlBRWxCTixHQUFHLENBQUMsQ0FBQ0MsTUFBVzRFOzRDQUNmLHFCQUNFLDhEQUFDdEosa0ZBQXdCQTtnREFFdkJvSixTQUFTNUcsZ0JBQWdCeUUsUUFBUSxDQUFDdkMsS0FBS0ssS0FBSztnREFDNUNzRSxpQkFBaUIsSUFDZnpDLHNCQUFzQmxDLEtBQUtLLEtBQUssRUFBRUwsS0FBS0ksVUFBVTtnREFFbkRoRCxXQUFVO2dEQUNWbUgsVUFBVSxDQUFDN0MsSUFBTUEsRUFBRXVCLGNBQWM7MERBRWhDakQsS0FBS0ksVUFBVTsrQ0FSWHdFOzs7Ozt3Q0FXWDs7Ozs7Ozs7Ozs7Ozs7Ozs7O29CQU1UbEIsY0FBY3BDLE1BQU0sR0FBRyxtQkFDdEI7a0NBQ0dvQyxjQUFjM0QsR0FBRyxDQUFDLENBQUNpQixLQUFLZjtnQ0FhZEwsNkJBaUNHQTttQ0E3Q1pvQixDQUFBQSxnQkFBQUEsMEJBQUFBLElBQUtYLEtBQUssTUFBSyx1QkFDYiw4REFBQzREO2dDQUFnQjdHLFdBQVU7MENBQ3hCVDsrQkFET3NEOzs7OzBEQUlWLDhEQUFDZ0U7Z0NBRUM3RyxXQUFVOzBDQUVWLDRFQUFDNkc7b0NBQUk3RyxXQUFVOzhDQUNiLDRFQUFDNkc7d0NBQUk3RyxXQUFVOzs0Q0FFWndDLEVBQUFBLDhCQUFBQSxXQUFXLENBQUNvQixJQUFJWixVQUFVLENBQUMsY0FBM0JSLGtEQUFBQSw0QkFBNkIwQixNQUFNLElBQUcsbUJBQ3JDOzBEQUNHMUIsV0FBVyxDQUFDb0IsSUFBSVosVUFBVSxDQUFDLENBQUNMLEdBQUcsQ0FBQyxDQUFDdUQsTUFBTXVCLGtCQUN0Qyw4REFBQ1o7d0RBRUM3RyxXQUFVOzswRUFFViw4REFBQzBIO2dFQUFLMUgsV0FBVTswRUFDYmtHOzs7Ozs7MEVBRUgsOERBQUN5QjtnRUFDQ0MsU0FBUyxDQUFDdEQ7b0VBQ1JBLEVBQUV1QixjQUFjO29FQUNoQkksaUJBQWlCckMsSUFBSVosVUFBVSxFQUFFa0Q7Z0VBQ25DO2dFQUNBbEcsV0FBVTswRUFDWDs7Ozs7Ozt1REFaSXlIOzs7Ozs7MERBbUJiLDhEQUFDNUksb0VBQVdBO2dEQUNWNkYsT0FBT3BDLFdBQVcsQ0FBQ3NCLElBQUlaLFVBQVUsQ0FBQyxJQUFJO2dEQUN0QytELFVBQVUsQ0FBQ3pDLElBQ1QvQixlQUFlO3dEQUNiLEdBQUdELFdBQVc7d0RBQ2QsQ0FBQ3NCLElBQUlaLFVBQVUsQ0FBQyxFQUFFc0IsRUFBRUcsTUFBTSxDQUFDQyxLQUFLO29EQUNsQztnREFFRm1ELFdBQVcsQ0FBQ3ZELElBQU1vQixjQUFjcEIsR0FBR1YsSUFBSVosVUFBVTtnREFDakQ4RSxhQUNFdEYsRUFBQUEsK0JBQUFBLFdBQVcsQ0FBQ29CLElBQUlaLFVBQVUsQ0FBQyxjQUEzQlIsbURBQUFBLDZCQUE2QjBCLE1BQU0sSUFBRyxJQUNsQyxLQUNBLFVBQXlCLE9BQWZOLElBQUlaLFVBQVUsRUFBQztnREFFL0JoRCxXQUFVOzs7Ozs7Ozs7Ozs7Ozs7OzsrQkEzQ1g2Qzs7Ozs7OztvQkFvRGR6RCxVQUFVa0gsY0FBY3BDLE1BQU0sR0FBRyxtQkFDaEM7a0NBQ0dvQyxjQUFjM0QsR0FBRyxDQUFDLENBQUN5RSxRQUFhdkU7MENBQy9CLDhEQUFDeEUsdURBQUtBO2dDQUVKeUosYUFDRTdILHFCQUFxQkEscUJBQXFCUDtnQ0FFNUNnRixPQUFPcEMsV0FBVyxDQUFDOEUsT0FBT3BFLFVBQVUsQ0FBQyxJQUFJO2dDQUN6QytELFVBQVUsQ0FBQ3pDLElBQ1QvQixlQUFlO3dDQUNiLEdBQUdELFdBQVc7d0NBQ2QsQ0FBQzhFLE9BQU9wRSxVQUFVLENBQUMsRUFBRXNCLEVBQUVHLE1BQU0sQ0FBQ0MsS0FBSztvQ0FDckM7Z0NBRUZtRCxXQUFXLENBQUN2RCxJQUFNb0IsY0FBY3BCLEdBQUc4QyxPQUFPcEUsVUFBVTtnQ0FDcERoRCxXQUFVOytCQVpMNkM7Ozs7O3dCQWNUOztvQkFHSHhELHlCQUNDO2tDQUNHaUgsY0FBYzNELEdBQUcsQ0FBQyxDQUFDeUUsUUFBYXZFOzBDQUMvQiw4REFBQ3hFLHVEQUFLQTtnQ0FFSnlKLGFBQWEsR0FBa0IsT0FBZm5JO2dDQUNoQitFLE9BQU9wQyxXQUFXLENBQUM4RSxPQUFPcEUsVUFBVSxDQUFDLElBQUk7Z0NBQ3pDK0QsVUFBVSxDQUFDekMsSUFDVC9CLGVBQWU7d0NBQ2IsR0FBR0QsV0FBVzt3Q0FDZCxDQUFDOEUsT0FBT3BFLFVBQVUsQ0FBQyxFQUFFc0IsRUFBRUcsTUFBTSxDQUFDQyxLQUFLO29DQUNyQztnQ0FFRm1ELFdBQVcsQ0FBQ3ZELElBQU1vQixjQUFjcEIsR0FBRzhDLE9BQU9wRSxVQUFVO2dDQUNwRGhELFdBQVU7K0JBVkw2Qzs7Ozs7d0JBWVQ7Ozs7Ozs7O1lBS0xyRCx1QkFBUyw4REFBQ3FIOzBCQUFLcEg7Ozs7OzswQkFFaEIsOERBQUNvSDtnQkFBSTdHLFdBQVdwQiwrQ0FBRUEsQ0FBQyxJQUFJb0I7MEJBQ3JCLDRFQUFDNkc7b0JBQ0M3RyxXQUFVO29CQUNWK0gsT0FBTzt3QkFBRUMsUUFBUTt3QkFBUzdFLE9BQU87b0JBQU87OEJBRXhDLDRFQUFDNUUsc0RBQVdBO3dCQUNWMEosS0FBSzFIO3dCQUNMMkgsU0FBU3hGO3dCQUNUeUYsWUFBWXBGO3dCQUNacUYsY0FBYzt3QkFDZEMsdUJBQXVCekI7d0JBQ3ZCMEIsYUFBYSxDQUFDekg7NEJBQ1pBLE9BQU9tRCxHQUFHLENBQUN1RSxnQkFBZ0I7NEJBQzNCLDhCQUE4Qjs0QkFDOUIsSUFBSXJKLFdBQVc7Z0NBQ2IyQixPQUFPbUQsR0FBRyxDQUFDQyxrQkFBa0I7NEJBQy9CLE9BQU8sSUFBSSxDQUFDdkIsaUJBQWlCQSxjQUFjd0IsTUFBTSxLQUFLLEdBQUc7Z0NBQ3ZEckQsT0FBT21ELEdBQUcsQ0FBQ0csaUJBQWlCOzRCQUM5QixPQUFPO2dDQUNMdEQsT0FBT21ELEdBQUcsQ0FBQ0ksV0FBVzs0QkFDeEI7d0JBQ0Y7d0JBQ0FvRSxxQkFBcUIsQ0FBQzNIOzRCQUNwQkEsT0FBT21ELEdBQUcsQ0FBQ3VFLGdCQUFnQjt3QkFDN0I7d0JBQ0FFLGlCQUFpQixDQUFDbEQ7NEJBQ2hCQSxNQUFNdkIsR0FBRyxDQUFDdUUsZ0JBQWdCO3dCQUM1Qjt3QkFDQUcsbUJBQW1CLENBQUM3SDs0QkFDbEJBLE9BQU9tRCxHQUFHLENBQUN1RSxnQkFBZ0I7d0JBQzdCO3dCQUNBSSxlQUFlOzRCQUNiekYsVUFBVTs0QkFDVjBGLFdBQVc7NEJBQ1h0RixXQUFXO2dDQUFFdUYsYUFBYTs0QkFBaUI7d0JBQzdDOzs7Ozs7Ozs7Ozs7Ozs7O1lBS0w1SixzQkFDQyw4REFBQ1gsbURBQVVBO2dCQUNUMkMsYUFDRW5CLGFBQ0lvQixPQUFPTCxPQUFPTSxHQUFHLENBQUMsWUFBWSxJQUM5QixDQUFDWixFQUFBQSxtQkFBQUEsUUFBUXdELE9BQU8sY0FBZnhELHdDQUFBQSx1QkFBQUEsaUJBQWlCeUQsR0FBRyxjQUFwQnpELDJDQUFBQSxxQkFBc0J1SSx3QkFBd0IsT0FBTSxLQUFLO2dCQUVoRWhKLFlBQ0VBLGFBQ0lBLGFBQ0FTLEVBQUFBLG9CQUFBQSxRQUFRd0QsT0FBTyxjQUFmeEQseUNBQUFBLHdCQUFBQSxrQkFBaUJ5RCxHQUFHLGNBQXBCekQsNENBQUFBLHNCQUFzQndJLHVCQUF1QixPQUFNO2dCQUV6REMsY0FBYyxDQUFDeEk7b0JBQ2IsSUFBSUQsUUFBUXdELE9BQU8sRUFBRTs0QkFDbkJ4RCxzQkFBQUE7d0JBQUFBLG9CQUFBQSwrQkFBQUEsbUJBQUFBLFFBQVN3RCxPQUFPLGNBQWhCeEQsd0NBQUFBLHVCQUFBQSxpQkFBa0J5RCxHQUFHLGNBQXJCekQsMkNBQUFBLHFCQUF1QjBJLGtCQUFrQixDQUFDekksT0FBTztvQkFDbkQ7b0JBRUEsSUFBSVYsWUFBWTt3QkFDZGUsT0FBTzhELEdBQUcsQ0FBQyxRQUFRbkUsS0FBS29FLFFBQVE7d0JBQ2hDNUQsUUFBUSxHQUFlSCxPQUFaRSxVQUFTLEtBQXFCLE9BQWxCRixPQUFPK0QsUUFBUTtvQkFDeEM7Z0JBQ0Y7Ozs7Ozs7Ozs7OztBQUtWO0dBamdCTTdGOztRQTJCaUJoQiw0REFBZUE7UUFFbkJDLHdEQUFXQTtRQUNSRixzREFBU0E7OztLQTlCekJpQjtBQW1nQk4sK0RBQWVBLGFBQWFBLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL19jb21wb25lbnQvRGF0YUdyaWRUYWJsZS50c3g/ZjRmOSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIjtcclxuaW1wb3J0IFJlYWN0LCB7IHVzZVN0YXRlLCB1c2VFZmZlY3QsIHVzZVJlZiwgdXNlQ29udGV4dCwgdXNlTWVtbyB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBGYUZpbHRlciwgRmFTZWFyY2ggfSBmcm9tIFwicmVhY3QtaWNvbnMvZmFcIjtcclxuaW1wb3J0IHsgdXNlUm91dGVyLCB1c2VTZWFyY2hQYXJhbXMsIHVzZVBhdGhuYW1lIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xyXG5pbXBvcnQge1xyXG4gIERyb3Bkb3duTWVudSxcclxuICBEcm9wZG93bk1lbnVDaGVja2JveEl0ZW0sXHJcbiAgRHJvcGRvd25NZW51Q29udGVudCxcclxuICBEcm9wZG93bk1lbnVUcmlnZ2VyLFxyXG59IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvZHJvcGRvd24tbWVudVwiO1xyXG5pbXBvcnQgeyBJbnB1dCB9IGZyb20gXCJAL2NvbXBvbmVudHMvdWkvaW5wdXRcIjtcclxuaW1wb3J0IFBhZ2luYXRpb24gZnJvbSBcIi4vUGFnaW5hdGlvblwiO1xyXG5pbXBvcnQgeyBBZ0dyaWRSZWFjdCB9IGZyb20gXCJhZy1ncmlkLXJlYWN0XCI7XHJcbmltcG9ydCBcImFnLWdyaWQtY29tbXVuaXR5L3N0eWxlcy9hZy1ncmlkLmNzc1wiO1xyXG5pbXBvcnQgXCJhZy1ncmlkLWNvbW11bml0eS9zdHlsZXMvYWctdGhlbWUtYWxwaW5lLmNzc1wiO1xyXG5pbXBvcnQgeyBBbGxDb21tdW5pdHlNb2R1bGUsIE1vZHVsZVJlZ2lzdHJ5IH0gZnJvbSBcImFnLWdyaWQtY29tbXVuaXR5XCI7XHJcbmltcG9ydCB7IFdvcmtSZXBvcnRDb250ZXh0IH0gZnJvbSBcIi4uL3Btcy9tYW5hZ2Vfd29ya19yZXBvcnQvV29ya1JlcG9ydENvbnRleHRcIjtcclxuaW1wb3J0IHsgVHJhY2tlckNvbnRleHQgfSBmcm9tIFwiLi4vdXNlci90cmFja2VyL1RyYWNrZXJDb250ZXh0XCI7XHJcbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCI7XHJcbmltcG9ydCB7IEN1c3RvbUlucHV0IH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9jdXN0b21JbnB1dFwiO1xyXG5cclxuLy8gUmVnaXN0ZXIgQUcgR3JpZCBtb2R1bGVzXHJcbk1vZHVsZVJlZ2lzdHJ5LnJlZ2lzdGVyTW9kdWxlcyhbQWxsQ29tbXVuaXR5TW9kdWxlXSk7XHJcblxyXG5pbnRlcmZhY2UgRGF0YUdyaWRUYWJsZVByb3BzIHtcclxuICBjb2x1bW5zOiBhbnlbXTtcclxuICBkYXRhOiBhbnlbXTtcclxuICBpc0xvYWRpbmc/OiBib29sZWFuO1xyXG4gIHNob3dDb2xEcm9wRG93bnM/OiBib29sZWFuO1xyXG4gIGZpbHRlcj86IGJvb2xlYW47XHJcbiAgZmlsdGVyMVBsYWNlSG9sZGVyPzogc3RyaW5nO1xyXG4gIGZpbHRlcjI/OiBib29sZWFuO1xyXG4gIGZpbHRlcjM/OiBib29sZWFuO1xyXG4gIGZpbHRlcjN2aWV3PzogSlNYLkVsZW1lbnQ7XHJcbiAgdG90YWw/OiBib29sZWFuO1xyXG4gIHRvdGFsdmlldz86IEpTWC5FbGVtZW50O1xyXG4gIGZpbHRlcl9jb2x1bW4/OiBzdHJpbmc7XHJcbiAgZmlsdGVyX2NvbHVtbjI/OiBzdHJpbmc7XHJcbiAgZmlsdGVyX2NvbHVtbjM/OiBzdHJpbmc7XHJcbiAgZmlsdGVyX2NvbHVtbjQ/OiBzdHJpbmc7XHJcbiAgb3ZlcmZsb3c/OiBib29sZWFuO1xyXG4gIHRvdGFsUGFnZXM/OiBudW1iZXI7XHJcbiAgc2hvd1BhZ2VFbnRyaWVzPzogYm9vbGVhbjtcclxuICBjbGFzc05hbWU/OiBzdHJpbmc7XHJcbiAgc2hvd1NlYXJjaENvbHVtbj86IGJvb2xlYW47XHJcbiAgcGFnZVNpemU/OiBudW1iZXI7XHJcbiAgaXNUaW1lclJ1bm5pbmc/OiBhbnk7XHJcbiAgc2V0SXNUaW1lclJ1bm5pbmc/OiBhbnk7XHJcbiAgb25GaWx0ZXJlZERhdGFDaGFuZ2U/OiAoZmlsdGVyZWREYXRhOiBhbnlbXSkgPT4gdm9pZDtcclxufVxyXG5cclxuY29uc3QgRGF0YUdyaWRUYWJsZSA9ICh7XHJcbiAgY29sdW1ucyxcclxuICBkYXRhLFxyXG4gIGlzTG9hZGluZyxcclxuICBzaG93Q29sRHJvcERvd25zLFxyXG4gIGZpbHRlcixcclxuICBmaWx0ZXIyLFxyXG4gIGZpbHRlcjMgPSBmYWxzZSxcclxuICBmaWx0ZXIzdmlldyxcclxuICB0b3RhbCxcclxuICB0b3RhbHZpZXcsXHJcbiAgZmlsdGVyX2NvbHVtbixcclxuICBmaWx0ZXJfY29sdW1uMixcclxuICBmaWx0ZXJfY29sdW1uMyxcclxuICBmaWx0ZXJfY29sdW1uNCxcclxuICB0b3RhbFBhZ2VzLFxyXG4gIHNob3dQYWdlRW50cmllcyxcclxuICBjbGFzc05hbWUsXHJcbiAgZmlsdGVyMVBsYWNlSG9sZGVyLFxyXG4gIHNob3dTZWFyY2hDb2x1bW4gPSB0cnVlLFxyXG4gIHBhZ2VTaXplLFxyXG4gIGlzVGltZXJSdW5uaW5nLFxyXG4gIHNldElzVGltZXJSdW5uaW5nLFxyXG4gIG9uRmlsdGVyZWREYXRhQ2hhbmdlLFxyXG59OiBEYXRhR3JpZFRhYmxlUHJvcHMpID0+IHtcclxuICBjb25zdCBbcGFnZSwgc2V0UGFnZV0gPSB1c2VTdGF0ZTxudW1iZXI+KHBhZ2VTaXplIHx8IDUwKTtcclxuICBjb25zdCBbc2VsZWN0ZWRDb2x1bW5zLCBzZXRTZWxlY3RlZENvbHVtbnNdID0gdXNlU3RhdGU8c3RyaW5nW10+KFtdKTtcclxuICBjb25zdCBzZWFyY2hQYXJhbXMgPSB1c2VTZWFyY2hQYXJhbXMoKTtcclxuICBjb25zdCBwYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKHNlYXJjaFBhcmFtcyk7XHJcbiAgY29uc3QgcGF0aG5hbWUgPSB1c2VQYXRobmFtZSgpO1xyXG4gIGNvbnN0IHsgcmVwbGFjZSB9ID0gdXNlUm91dGVyKCk7XHJcblxyXG4gIGNvbnN0IGN1cnJlbnRQYWdlID0gTnVtYmVyKHNlYXJjaFBhcmFtcy5nZXQoXCJwYWdlXCIpKSB8fCAxO1xyXG4gIGNvbnN0IGN1cnJlbnRQYWdlU2l6ZSA9XHJcbiAgICBOdW1iZXIoc2VhcmNoUGFyYW1zLmdldChcInBhZ2VTaXplXCIpKSB8fCBwYWdlU2l6ZSB8fCA1MDtcclxuXHJcbiAgY29uc3QgW3BhZ2luYXRpb24sIHNldFBhZ2luYXRpb25dID0gdXNlU3RhdGU8YW55Pih7XHJcbiAgICBwYWdlSW5kZXg6IDAsXHJcbiAgICBwYWdlU2l6ZTogcGFnZSxcclxuICB9KTtcclxuXHJcbiAgY29uc3QgeyBzZXRGcm9tRGF0ZSwgZnJvbURhdGUsIHRvRGF0ZSwgc2V0VG9EYXRlIH0gPVxyXG4gICAgdXNlQ29udGV4dChXb3JrUmVwb3J0Q29udGV4dCk7XHJcbiAgY29uc3QgeyBzZXRGRGF0ZSwgZkRhdGUsIHREYXRlLCBzZXRURGF0ZSB9ID0gdXNlQ29udGV4dChUcmFja2VyQ29udGV4dCk7XHJcblxyXG4gIGNvbnN0IFtjb2x1bW5TdGF0ZSwgc2V0Q29sdW1uU3RhdGVdID0gdXNlU3RhdGU8YW55W10+KFtdKTtcclxuICBjb25zdCBncmlkUmVmID0gdXNlUmVmPEFnR3JpZFJlYWN0PihudWxsKTtcclxuXHJcbiAgY29uc3QgW2NvbHVtbkRhdGEsIHNldENvbHVtbkRhdGFdID0gdXNlU3RhdGUoXCJcIik7XHJcbiAgY29uc3QgW2NvbHVtblZpc2liaWxpdHksIHNldENvbHVtblZpc2liaWxpdHldID0gdXNlU3RhdGU8YW55Pih7fSk7XHJcblxyXG4gIGNvbnN0IFtpbnB1dFZhbHVlcywgc2V0SW5wdXRWYWx1ZXNdID0gdXNlU3RhdGU8eyBba2V5OiBzdHJpbmddOiBzdHJpbmcgfT4oe30pO1xyXG5cclxuICBjb25zdCBbc2VhcmNoVGVybXMsIHNldFNlYXJjaFRlcm1zXSA9IHVzZVN0YXRlPHsgW2tleTogc3RyaW5nXTogc3RyaW5nW10gfT4oXHJcbiAgICB7fVxyXG4gICk7XHJcblxyXG4gIC8vIFByZXBhcmUgZGF0YSB3aXRoIHN0YWJsZSBzZXJpYWwgbnVtYmVyc1xyXG4gIGNvbnN0IHByb2Nlc3NlZERhdGEgPSB1c2VNZW1vKCgpID0+IHtcclxuICAgIHJldHVybiBkYXRhPy5tYXAoKGl0ZW0sIGluZGV4KSA9PiAoe1xyXG4gICAgICAuLi5pdGVtLFxyXG4gICAgICBzdGFibGVJZDogKGN1cnJlbnRQYWdlIC0gMSkgKiBjdXJyZW50UGFnZVNpemUgKyBpbmRleCArIDEsXHJcbiAgICB9KSk7XHJcbiAgfSwgW2RhdGEsIGN1cnJlbnRQYWdlLCBjdXJyZW50UGFnZVNpemVdKTtcclxuXHJcbiAgLy8gUHJlcGFyZSBjb2x1bW5zIHdpdGggc2VyaWFsIG51bWJlclxyXG4gIGNvbnN0IGNvbHVtbnNXaXRoU2VyaWFsTnVtYmVyID0gdXNlTWVtbygoKSA9PiB7XHJcbiAgICByZXR1cm4gW1xyXG4gICAgICB7XHJcbiAgICAgICAgaGVhZGVyTmFtZTogXCJTci4gTm8uXCIsXHJcbiAgICAgICAgZmllbGQ6IFwic3RhYmxlSWRcIixcclxuICAgICAgICBzb3J0YWJsZTogdHJ1ZSxcclxuICAgICAgICB3aWR0aDogODAsXHJcbiAgICAgICAgbWluV2lkdGg6IDgwLCAvLyBQcmV2ZW50IHNocmlua2luZ1xyXG4gICAgICAgIG1heFdpZHRoOiA4MCwgLy8gUHJldmVudCBleHBhbmRpbmdcclxuICAgICAgICBjZWxsU3R5bGU6IHtcclxuICAgICAgICAgIHRleHRBbGlnbjogXCJjZW50ZXJcIixcclxuICAgICAgICB9LFxyXG4gICAgICAgIHBpbm5lZDogXCJsZWZ0XCIsXHJcblxyXG4gICAgICAgIGNvbXBhcmF0b3I6ICh2YWx1ZUE6IG51bWJlciwgdmFsdWVCOiBudW1iZXIpID0+IHZhbHVlQSAtIHZhbHVlQixcclxuICAgICAgfSxcclxuICAgICAgLi4uY29sdW1ucy5maWx0ZXIoKGNvbCkgPT4gY29sLmZpZWxkICE9PSBcInNyX25vXCIpLFxyXG4gICAgXTtcclxuICB9LCBbY29sdW1uc10pO1xyXG5cclxuICAvLyBJbml0aWFsaXplIGFsbCBjb2x1bW5zIGFzIHZpc2libGVcclxuICB1c2VFZmZlY3QoKCkgPT4ge1xyXG4gICAgY29uc3QgaW5pdGlhbFZpc2liaWxpdHk6IGFueSA9IHt9O1xyXG4gICAgY29sdW1uc1dpdGhTZXJpYWxOdW1iZXIuZm9yRWFjaCgoY29sKSA9PiB7XHJcbiAgICAgIGluaXRpYWxWaXNpYmlsaXR5W2NvbC5maWVsZF0gPSB0cnVlO1xyXG4gICAgfSk7XHJcbiAgICBzZXRDb2x1bW5WaXNpYmlsaXR5KGluaXRpYWxWaXNpYmlsaXR5KTtcclxuICB9LCBbY29sdW1uc1dpdGhTZXJpYWxOdW1iZXJdKTtcclxuXHJcbiAgdXNlRWZmZWN0KCgpID0+IHtcclxuICAgIGlmIChjb2x1bW5EYXRhICE9PSBcImRhdGVcIikge1xyXG4gICAgICBzZXRGcm9tRGF0ZShcIlwiKSwgc2V0VG9EYXRlKFwiXCIpO1xyXG4gICAgICBzZXRGRGF0ZShcIlwiKSwgc2V0VERhdGUoXCJcIik7XHJcbiAgICB9XHJcbiAgfSwgW2NvbHVtbkRhdGFdKTtcclxuXHJcbiAgLy8gU2hvdyBvciBoaWRlIG92ZXJsYXlzIGJhc2VkIG9uIGxvYWRpbmcgYW5kIGRhdGEgc3RhdGVcclxuICB1c2VNZW1vKCgpID0+IHtcclxuICAgIGlmIChncmlkUmVmLmN1cnJlbnQgJiYgZ3JpZFJlZi5jdXJyZW50LmFwaSkge1xyXG4gICAgICBpZiAoaXNMb2FkaW5nKSB7XHJcbiAgICAgICAgZ3JpZFJlZi5jdXJyZW50LmFwaS5zaG93TG9hZGluZ092ZXJsYXkoKTtcclxuICAgICAgfSBlbHNlIGlmICghcHJvY2Vzc2VkRGF0YSB8fCBwcm9jZXNzZWREYXRhLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICAgIGdyaWRSZWYuY3VycmVudC5hcGkuc2hvd05vUm93c092ZXJsYXkoKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICBncmlkUmVmLmN1cnJlbnQuYXBpLmhpZGVPdmVybGF5KCk7XHJcbiAgICAgIH1cclxuICAgIH1cclxuICB9LCBbaXNMb2FkaW5nLCBwcm9jZXNzZWREYXRhXSk7XHJcblxyXG4gIC8vIEhhbmRsZSBwYWdlIGNoYW5nZVxyXG4gIGNvbnN0IGhhbmRsZVBhZ2VDaGFuZ2UgPSAoZTogYW55KSA9PiB7XHJcbiAgICBjb25zdCBuZXdQYWdlU2l6ZSA9IHBhcnNlSW50KGUudGFyZ2V0LnZhbHVlKTtcclxuICAgIHNldFBhZ2UobmV3UGFnZVNpemUpO1xyXG4gICAgaWYgKHRvdGFsUGFnZXMpIHtcclxuICAgICAgcGFyYW1zLnNldChcInBhZ2VTaXplXCIsIG5ld1BhZ2VTaXplPy50b1N0cmluZygpKTtcclxuICAgICAgcmVwbGFjZShgJHtwYXRobmFtZX0/JHtwYXJhbXMudG9TdHJpbmcoKX1gKTtcclxuICAgIH1cclxuICAgIHNldFBhZ2luYXRpb24oKHByZXZTdGF0ZSkgPT4gKHtcclxuICAgICAgLi4ucHJldlN0YXRlLFxyXG4gICAgICBwYWdlU2l6ZTogbmV3UGFnZVNpemUsXHJcbiAgICB9KSk7XHJcbiAgfTtcclxuXHJcbiAgLy8gSGFuZGxlIGNvbHVtbiBzZWxlY3Rpb25cclxuICBjb25zdCBoYW5kbGVDb2x1bW5TZWxlY3Rpb24gPSAoY29sdW1uS2V5OiBzdHJpbmcsIGNvbHVtbkhlYWRlcjogc3RyaW5nKSA9PiB7XHJcbiAgICBzZXRTZWxlY3RlZENvbHVtbnMoKHByZXZTZWxlY3RlZENvbHVtbnMpID0+IHtcclxuICAgICAgbGV0IHVwZGF0ZWRDb2x1bW5zO1xyXG5cclxuICAgICAgaWYgKHByZXZTZWxlY3RlZENvbHVtbnMuaW5jbHVkZXMoY29sdW1uS2V5KSkge1xyXG4gICAgICAgIHVwZGF0ZWRDb2x1bW5zID0gcHJldlNlbGVjdGVkQ29sdW1ucy5maWx0ZXIoKGNvbCkgPT4gY29sICE9PSBjb2x1bW5LZXkpO1xyXG5cclxuICAgICAgICBjb25zdCB1cGRhdGVkUGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyhzZWFyY2hQYXJhbXMpO1xyXG5cclxuICAgICAgICBpZiAoY29sdW1uSGVhZGVyID09PSBcIkRhdGVcIikge1xyXG4gICAgICAgICAgdXBkYXRlZFBhcmFtcy5kZWxldGUoXCJmRGF0ZVwiKTtcclxuICAgICAgICAgIHVwZGF0ZWRQYXJhbXMuZGVsZXRlKFwidERhdGVcIik7XHJcbiAgICAgICAgfSBlbHNlIHtcclxuICAgICAgICAgIHVwZGF0ZWRQYXJhbXMuZGVsZXRlKGNvbHVtbkhlYWRlcik7XHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICByZXBsYWNlKGAke3BhdGhuYW1lfT8ke3VwZGF0ZWRQYXJhbXMudG9TdHJpbmcoKX1gKTtcclxuICAgICAgfSBlbHNlIHtcclxuICAgICAgICB1cGRhdGVkQ29sdW1ucyA9IFsuLi5wcmV2U2VsZWN0ZWRDb2x1bW5zLCBjb2x1bW5LZXldO1xyXG5cclxuICAgICAgICBjb25zdCB1cGRhdGVkUGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyhzZWFyY2hQYXJhbXMpO1xyXG4gICAgICAgIHVwZGF0ZWRQYXJhbXMuc2V0KGNvbHVtbkhlYWRlciwgXCJcIik7XHJcbiAgICAgICAgcmVwbGFjZShgJHtwYXRobmFtZX0/JHt1cGRhdGVkUGFyYW1zLnRvU3RyaW5nKCl9YCk7XHJcbiAgICAgIH1cclxuXHJcbiAgICAgIHJldHVybiB1cGRhdGVkQ29sdW1ucztcclxuICAgIH0pO1xyXG4gIH07XHJcblxyXG4gIC8vIFNlYXJjaCBmdW5jdGlvbmFsaXR5XHJcbiAgY29uc3QgaGFuZGxlU2VhcmNoQ2hhbmdlID0gKFxyXG4gICAgZXZlbnQ6IFJlYWN0LkNoYW5nZUV2ZW50PEhUTUxJbnB1dEVsZW1lbnQ+LFxyXG4gICAgY29sdW1uTmFtZTogc3RyaW5nXHJcbiAgKSA9PiB7XHJcbiAgICBjb25zdCB2YWx1ZSA9IGV2ZW50LnRhcmdldC52YWx1ZS50cmltKCk7XHJcblxyXG4gICAgaWYgKHZhbHVlKSB7XHJcbiAgICAgIGNvbnN0IHVwZGF0ZWRQYXJhbXMgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKHNlYXJjaFBhcmFtcyk7XHJcbiAgICAgIHVwZGF0ZWRQYXJhbXMuc2V0KGNvbHVtbk5hbWUsIHZhbHVlKTtcclxuICAgICAgdXBkYXRlZFBhcmFtcy5zZXQoXCJwYWdlXCIsIFwiMVwiKTtcclxuICAgICAgcmVwbGFjZShgJHtwYXRobmFtZX0/JHt1cGRhdGVkUGFyYW1zLnRvU3RyaW5nKCl9YCk7XHJcbiAgICB9IGVsc2Uge1xyXG4gICAgICBjb25zdCB1cGRhdGVkUGFyYW1zID0gbmV3IFVSTFNlYXJjaFBhcmFtcyhzZWFyY2hQYXJhbXMpO1xyXG4gICAgICB1cGRhdGVkUGFyYW1zLmRlbGV0ZShjb2x1bW5OYW1lKTtcclxuICAgICAgcmVwbGFjZShgJHtwYXRobmFtZX0/JHt1cGRhdGVkUGFyYW1zLnRvU3RyaW5nKCl9YCk7XHJcbiAgICB9XHJcbiAgfTtcclxuXHJcbiAgY29uc3QgaGFuZGxlS2V5RG93biA9IChcclxuICAgIGU6IFJlYWN0LktleWJvYXJkRXZlbnQ8SFRNTElucHV0RWxlbWVudD4sXHJcbiAgICBjb2x1bW5OYW1lOiBzdHJpbmdcclxuICApID0+IHtcclxuICAgIGNvbnN0IGN1cnJlbnRJbnB1dCA9IGlucHV0VmFsdWVzW2NvbHVtbk5hbWVdPy50cmltKCk7XHJcblxyXG4gICAgaWYgKGUua2V5ID09PSBcIkVudGVyXCIpIHtcclxuICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xyXG4gICAgICBpZiAoY3VycmVudElucHV0ICYmICFzZWFyY2hUZXJtc1tjb2x1bW5OYW1lXT8uaW5jbHVkZXMoY3VycmVudElucHV0KSkge1xyXG4gICAgICAgIGNvbnN0IHVwZGF0ZWQgPSBbLi4uKHNlYXJjaFRlcm1zW2NvbHVtbk5hbWVdIHx8IFtdKSwgY3VycmVudElucHV0XTtcclxuICAgICAgICB1cGRhdGVTZWFyY2hQYXJhbXMoY29sdW1uTmFtZSwgdXBkYXRlZCk7XHJcbiAgICAgICAgc2V0U2VhcmNoVGVybXMoeyAuLi5zZWFyY2hUZXJtcywgW2NvbHVtbk5hbWVdOiB1cGRhdGVkIH0pO1xyXG4gICAgICAgIHNldElucHV0VmFsdWVzKHsgLi4uaW5wdXRWYWx1ZXMsIFtjb2x1bW5OYW1lXTogXCJcIiB9KTtcclxuICAgICAgfVxyXG4gICAgfVxyXG5cclxuICAgIGlmIChcclxuICAgICAgZS5rZXkgPT09IFwiQmFja3NwYWNlXCIgJiZcclxuICAgICAgIWN1cnJlbnRJbnB1dCAmJlxyXG4gICAgICBzZWFyY2hUZXJtc1tjb2x1bW5OYW1lXT8ubGVuZ3RoID4gMFxyXG4gICAgKSB7XHJcbiAgICAgIGNvbnN0IHVwZGF0ZWQgPSBbLi4uc2VhcmNoVGVybXNbY29sdW1uTmFtZV1dO1xyXG4gICAgICB1cGRhdGVkLnBvcCgpO1xyXG4gICAgICB1cGRhdGVTZWFyY2hQYXJhbXMoY29sdW1uTmFtZSwgdXBkYXRlZCk7XHJcbiAgICAgIHNldFNlYXJjaFRlcm1zKHsgLi4uc2VhcmNoVGVybXMsIFtjb2x1bW5OYW1lXTogdXBkYXRlZCB9KTtcclxuICAgIH1cclxuICB9O1xyXG5cclxuICBjb25zdCBoYW5kbGVSZW1vdmVUZXJtID0gKGNvbHVtbk5hbWU6IHN0cmluZywgdGVybTogc3RyaW5nKSA9PiB7XHJcbiAgICBjb25zdCB1cGRhdGVkID0gKHNlYXJjaFRlcm1zW2NvbHVtbk5hbWVdIHx8IFtdKS5maWx0ZXIoKHQpID0+IHQgIT09IHRlcm0pO1xyXG4gICAgdXBkYXRlU2VhcmNoUGFyYW1zKGNvbHVtbk5hbWUsIHVwZGF0ZWQpO1xyXG4gICAgc2V0U2VhcmNoVGVybXMoeyAuLi5zZWFyY2hUZXJtcywgW2NvbHVtbk5hbWVdOiB1cGRhdGVkIH0pO1xyXG4gIH07XHJcblxyXG4gIGNvbnN0IHVwZGF0ZVNlYXJjaFBhcmFtcyA9IChjb2x1bW5OYW1lOiBzdHJpbmcsIHRlcm1zOiBzdHJpbmdbXSkgPT4ge1xyXG4gICAgY29uc3QgdXBkYXRlZFBhcmFtcyA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoc2VhcmNoUGFyYW1zKTtcclxuICAgIGlmICh0ZXJtcy5sZW5ndGggPiAwKSB7XHJcbiAgICAgIHVwZGF0ZWRQYXJhbXMuc2V0KGNvbHVtbk5hbWUsIHRlcm1zLmpvaW4oXCIsXCIpKTtcclxuICAgICAgdXBkYXRlZFBhcmFtcy5zZXQoXCJwYWdlXCIsIFwiMVwiKTtcclxuICAgIH0gZWxzZSB7XHJcbiAgICAgIHVwZGF0ZWRQYXJhbXMuZGVsZXRlKGNvbHVtbk5hbWUpO1xyXG4gICAgfVxyXG4gICAgcmVwbGFjZShgJHtwYXRobmFtZX0/JHt1cGRhdGVkUGFyYW1zLnRvU3RyaW5nKCl9YCk7XHJcbiAgfTtcclxuXHJcbiAgLy8gRmlsdGVyIGNvbHVtbnMgYmFzZWQgb24gc2VsZWN0ZWQgb25lc1xyXG4gIGNvbnN0IGZpbHRlckNvbHVtbnMgPSBzZWxlY3RlZENvbHVtbnMubGVuZ3RoXHJcbiAgICA/IHNlbGVjdGVkQ29sdW1ucy5tYXAoKGNvbHVtbktleSkgPT5cclxuICAgICAgICBjb2x1bW5zV2l0aFNlcmlhbE51bWJlci5maW5kKChjb2wpID0+IGNvbC5maWVsZCA9PT0gY29sdW1uS2V5KVxyXG4gICAgICApXHJcbiAgICA6IFtdO1xyXG5cclxuICBjb25zdCB0b2dnbGVDb2x1bW5WaXNpYmlsaXR5ID0gKGZpZWxkOiBzdHJpbmcsIGlzVmlzaWJsZTogYm9vbGVhbikgPT4ge1xyXG4gICAgc2V0Q29sdW1uVmlzaWJpbGl0eSgocHJldikgPT4gKHtcclxuICAgICAgLi4ucHJldixcclxuICAgICAgW2ZpZWxkXTogaXNWaXNpYmxlLFxyXG4gICAgfSkpO1xyXG5cclxuICAgIGlmIChncmlkUmVmLmN1cnJlbnQpIHtcclxuICAgICAgZ3JpZFJlZi5jdXJyZW50LmFwaS5zZXRDb2x1bW5zVmlzaWJsZShbZmllbGRdLCBpc1Zpc2libGUpO1xyXG4gICAgfVxyXG4gIH07XHJcblxyXG4gIC8vIEFkZCBhIGN1c3RvbSBtZXNzYWdlIGZvciBubyBkYXRhIGF2YWlsYWJsZVxyXG4gIGNvbnN0IG5vUm93c092ZXJsYXlUZW1wbGF0ZSA9ICc8c3BhbiBjbGFzcz1cImFnLW92ZXJsYXktbm8tcm93cy1jZW50ZXJcIj5ObyBEYXRhIEF2YWlsYWJsZTwvc3Bhbj4nO1xyXG5cclxuICByZXR1cm4gKFxyXG4gICAgPGRpdiBjbGFzc05hbWU9e2BhbmltYXRlLWluIGZhZGUtaW4gZHVyYXRpb24tMTAwMGB9PlxyXG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgganVzdGlmeS1iZXR3ZWVuIHctZnVsbCBsZzp3LWZ1bGwgbGc6ZmxleCBsZzpqdXN0aWZ5LWVuZCBtYi0zIGFuaW1hdGUtaW4gZmFkZS1pbiBkdXJhdGlvbi0xMDAwIGdhcC01XCI+XHJcbiAgICAgICAge3Nob3dQYWdlRW50cmllcyAmJiAoXHJcbiAgICAgICAgICA8c2VsZWN0XHJcbiAgICAgICAgICAgIHZhbHVlPXtwYWdlfSAvLyBTZXQgdGhlIHZhbHVlIHRvIGN1cnJlbnQgcGFnZSBzaXplXHJcbiAgICAgICAgICAgIG9uQ2hhbmdlPXtoYW5kbGVQYWdlQ2hhbmdlfVxyXG4gICAgICAgICAgICBjbGFzc05hbWU9XCIgYm9yZGVyLTIgcm91bmRlZC1tZCAgaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIGN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICAgID5cclxuICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT17MTB9PjEwPC9vcHRpb24+XHJcbiAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezE1fT4xNTwvb3B0aW9uPlxyXG4gICAgICAgICAgICA8b3B0aW9uIHZhbHVlPXsyNX0+MjU8L29wdGlvbj5cclxuICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT17NTB9PjUwPC9vcHRpb24+XHJcbiAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezEwMH0+MTAwPC9vcHRpb24+XHJcbiAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezI1MH0+MjUwPC9vcHRpb24+XHJcbiAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezUwMH0+NTAwPC9vcHRpb24+XHJcbiAgICAgICAgICAgIDxvcHRpb24gdmFsdWU9ezEwMDB9PjEwMDA8L29wdGlvbj5cclxuICAgICAgICAgICAgPG9wdGlvbiB2YWx1ZT17MTUwMH0+MTUwMDwvb3B0aW9uPlxyXG4gICAgICAgICAgICA8b3B0aW9uIHZhbHVlPXsyMDAwfT4yMDAwPC9vcHRpb24+XHJcbiAgICAgICAgICA8L3NlbGVjdD5cclxuICAgICAgICApfVxyXG5cclxuICAgICAgICB7c2hvd0NvbERyb3BEb3ducyAmJiAoXHJcbiAgICAgICAgICA8RHJvcGRvd25NZW51PlxyXG4gICAgICAgICAgICA8RHJvcGRvd25NZW51VHJpZ2dlciBhc0NoaWxkPlxyXG4gICAgICAgICAgICAgIDxwIGNsYXNzTmFtZT1cInAtMiBib3JkZXItMiByb3VuZGVkLW1kIGZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktY2VudGVyIHB4LTMgY3Vyc29yLXBvaW50ZXJcIj5cclxuICAgICAgICAgICAgICAgIDxGYUZpbHRlciAvPlxyXG4gICAgICAgICAgICAgIDwvcD5cclxuICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVUcmlnZ2VyPlxyXG4gICAgICAgICAgICA8RHJvcGRvd25NZW51Q29udGVudFxyXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cImJnLXdoaXRlIGJvcmRlci1ub25lIGRhcms6YmctZ3JheS05MDAgZGFyazpyaW5nLTEgZGFyazpyaW5nLWdyYXktODAwXCJcclxuICAgICAgICAgICAgICBvblNlbGVjdD17KGUpID0+IGUucHJldmVudERlZmF1bHQoKX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIHtjb2x1bW5zV2l0aFNlcmlhbE51bWJlclxyXG4gICAgICAgICAgICAgICAgLmZpbHRlcigoY29sdW1uKSA9PiBjb2x1bW4uaGlkZWFibGUgIT09IGZhbHNlKVxyXG4gICAgICAgICAgICAgICAgLm1hcCgoY29sdW1uKSA9PiAoXHJcbiAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVDaGVja2JveEl0ZW1cclxuICAgICAgICAgICAgICAgICAgICBrZXk9e2NvbHVtbi5maWVsZH1cclxuICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjYXBpdGFsaXplIGN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICAgICAgICAgICAgICBjaGVja2VkPXtjb2x1bW5WaXNpYmlsaXR5W2NvbHVtbi5maWVsZF19XHJcbiAgICAgICAgICAgICAgICAgICAgb25DaGVja2VkQ2hhbmdlPXsodmFsdWUpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICB0b2dnbGVDb2x1bW5WaXNpYmlsaXR5KGNvbHVtbi5maWVsZCwgdmFsdWUpXHJcbiAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgIG9uU2VsZWN0PXsoZSkgPT4gZS5wcmV2ZW50RGVmYXVsdCgpfVxyXG4gICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAge2NvbHVtbi5oZWFkZXJOYW1lIHx8IGNvbHVtbi5maWVsZH1cclxuICAgICAgICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVDaGVja2JveEl0ZW0+XHJcbiAgICAgICAgICAgICAgICApKX1cclxuICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVDb250ZW50PlxyXG4gICAgICAgICAgPC9Ecm9wZG93bk1lbnU+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAge3Nob3dTZWFyY2hDb2x1bW4gJiYgKFxyXG4gICAgICAgICAgPERyb3Bkb3duTWVudT5cclxuICAgICAgICAgICAgPERyb3Bkb3duTWVudVRyaWdnZXIgYXNDaGlsZD5cclxuICAgICAgICAgICAgICA8cCBjbGFzc05hbWU9XCJwLTIgYm9yZGVyLTIgcm91bmRlZC1tZCBmbGV4IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlciBweC0zIGN1cnNvci1wb2ludGVyXCI+XHJcbiAgICAgICAgICAgICAgICA8RmFTZWFyY2ggLz5cclxuICAgICAgICAgICAgICA8L3A+XHJcbiAgICAgICAgICAgIDwvRHJvcGRvd25NZW51VHJpZ2dlcj5cclxuICAgICAgICAgICAgPERyb3Bkb3duTWVudUNvbnRlbnRcclxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy13aGl0ZSBib3JkZXItbm9uZSBkYXJrOmJnLWdyYXktOTAwIGRhcms6cmluZy0xIGRhcms6cmluZy1ncmF5LTgwMFwiXHJcbiAgICAgICAgICAgICAgb25TZWxlY3Q9eyhlKSA9PiBlLnByZXZlbnREZWZhdWx0KCl9XHJcbiAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInB4LTMgcHktMlwiPlxyXG4gICAgICAgICAgICAgICAgPHAgY2xhc3NOYW1lPVwiZm9udC1zZW1pYm9sZCB0ZXh0LXNtXCI+U2VsZWN0IENvbHVtbnM8L3A+XHJcbiAgICAgICAgICAgICAgICB7Y29sdW1uc1dpdGhTZXJpYWxOdW1iZXJcclxuICAgICAgICAgICAgICAgICAgLmZpbHRlcihcclxuICAgICAgICAgICAgICAgICAgICAoaXRlbTogYW55KSA9PlxyXG4gICAgICAgICAgICAgICAgICAgICAgaXRlbS5maWVsZCAhPT0gXCJhY3Rpb25cIiAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgaXRlbS5maWVsZCAhPT0gXCJBY3Rpdml0eSBMb2dcIiAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgaXRlbS5maWVsZCAhPT0gXCJwYXVzZVwiICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICBpdGVtLmZpZWxkICE9PSBcImZpbmlzaF90aW1lXCIgJiZcclxuICAgICAgICAgICAgICAgICAgICAgIGl0ZW0uZmllbGQgIT09IFwic3RhcnRfdGltZVwiICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICBpdGVtLmZpZWxkICE9PSBcInRpbWVfc3BlbnRcIiAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgaXRlbS5maWVsZCAhPT0gXCJzcl9ub1wiICYmXHJcbiAgICAgICAgICAgICAgICAgICAgICBpdGVtLmZpZWxkICE9PSBcInBheW1lbnQgdGVybXNcIiAmJlxyXG4gICAgICAgICAgICAgICAgICAgICAgaXRlbS5maWVsZCAhPT0gXCJzdGFibGVJZFwiXHJcbiAgICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgICAgICAgLm1hcCgoaXRlbTogYW55LCBpZDogYW55KSA9PiB7XHJcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIChcclxuICAgICAgICAgICAgICAgICAgICAgIDxEcm9wZG93bk1lbnVDaGVja2JveEl0ZW1cclxuICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtpZH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgY2hlY2tlZD17c2VsZWN0ZWRDb2x1bW5zLmluY2x1ZGVzKGl0ZW0uZmllbGQpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkNoZWNrZWRDaGFuZ2U9eygpID0+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlQ29sdW1uU2VsZWN0aW9uKGl0ZW0uZmllbGQsIGl0ZW0uaGVhZGVyTmFtZSlcclxuICAgICAgICAgICAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCJjYXBpdGFsaXplIGN1cnNvci1wb2ludGVyXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgb25TZWxlY3Q9eyhlKSA9PiBlLnByZXZlbnREZWZhdWx0KCl9XHJcbiAgICAgICAgICAgICAgICAgICAgICA+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHtpdGVtLmhlYWRlck5hbWV9XHJcbiAgICAgICAgICAgICAgICAgICAgICA8L0Ryb3Bkb3duTWVudUNoZWNrYm94SXRlbT5cclxuICAgICAgICAgICAgICAgICAgICApO1xyXG4gICAgICAgICAgICAgICAgICB9KX1cclxuICAgICAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICAgICAgPC9Ecm9wZG93bk1lbnVDb250ZW50PlxyXG4gICAgICAgICAgPC9Ecm9wZG93bk1lbnU+XHJcbiAgICAgICAgKX1cclxuXHJcbiAgICAgICAge2ZpbHRlckNvbHVtbnMubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICA8PlxyXG4gICAgICAgICAgICB7ZmlsdGVyQ29sdW1ucy5tYXAoKGNvbCwgaW5kZXgpID0+XHJcbiAgICAgICAgICAgICAgY29sPy5maWVsZCA9PT0gXCJkYXRlXCIgPyAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2IGtleT17aW5kZXh9IGNsYXNzTmFtZT1cInctZnVsbCBtZDp3LWF1dG9cIj5cclxuICAgICAgICAgICAgICAgICAge2ZpbHRlcjN2aWV3fVxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKSA6IChcclxuICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAga2V5PXtpbmRleH1cclxuICAgICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZmxleCBmbGV4LSB3LWZ1bGwgbWQ6dy1bY2FsYygyMCUtMXJlbSldIG1pbi13LVsxMDBweF1cIlxyXG4gICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInJlbGF0aXZlXCI+XHJcbiAgICAgICAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGZsZXgtd3JhcCBpdGVtcy1jZW50ZXIgZ2FwLTEgZGFyazpiZy1ncmF5LTcwMCBib3JkZXIgYm9yZGVyLWdyYXktMzAwIGRhcms6Ym9yZGVyLWdyYXktNjAwIHJvdW5kZWQtbWQgIHB4LTEgZm9jdXMtd2l0aGluOnJpbmctMCBmb2N1cy13aXRoaW46cmluZy1ibHVlLTYwMCBmb2N1cy13aXRoaW46Ym9yZGVyLWJsdWUtNjAwIHNoYWRvdy1zbVwiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgey8qIFNlbGVjdGVkIHRlcm1zIGFzIHRhZ3MgaW5zaWRlIGlucHV0ICovfVxyXG4gICAgICAgICAgICAgICAgICAgICAge3NlYXJjaFRlcm1zW2NvbC5oZWFkZXJOYW1lXT8ubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDw+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAge3NlYXJjaFRlcm1zW2NvbC5oZWFkZXJOYW1lXS5tYXAoKHRlcm0sIGkpID0+IChcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxkaXZcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAga2V5PXtpfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCIgZmxleCBpdGVtcy1jZW50ZXIgYmctZ3JheS0xMDAgZGFyazpiZy1ncmF5LTYwMCB0ZXh0LWdyYXktODAwIGRhcms6dGV4dC1ncmF5LTIwMCBweC0yIHJvdW5kZWQtZnVsbCB0ZXh0LXhzXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwibXItMSB0cnVuY2F0ZSBtYXgtdy14c1wiPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIHt0ZXJtfVxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICA8L3NwYW4+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDxidXR0b25cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBvbkNsaWNrPXsoZSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgZS5wcmV2ZW50RGVmYXVsdCgpO1xyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaGFuZGxlUmVtb3ZlVGVybShjb2wuaGVhZGVyTmFtZSwgdGVybSk7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBjbGFzc05hbWU9XCIgIHRleHQtZ3JheS01MDAgZGFyazp0ZXh0LWdyYXktMzAwIGhvdmVyOnRleHQtZ3JheS03MDAgZGFyazpob3Zlcjp0ZXh0LWdyYXktMTAwXCJcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIMOXXHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIDwvYnV0dG9uPlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgKSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDwvPlxyXG4gICAgICAgICAgICAgICAgICAgICAgKX1cclxuICAgICAgICAgICAgICAgICAgICAgIDxDdXN0b21JbnB1dFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB2YWx1ZT17aW5wdXRWYWx1ZXNbY29sLmhlYWRlck5hbWVdIHx8IFwiXCJ9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT5cclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzZXRJbnB1dFZhbHVlcyh7XHJcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAuLi5pbnB1dFZhbHVlcyxcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIFtjb2wuaGVhZGVyTmFtZV06IGUudGFyZ2V0LnZhbHVlLFxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25LZXlEb3duPXsoZSkgPT4gaGFuZGxlS2V5RG93bihlLCBjb2wuaGVhZGVyTmFtZSl9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtcclxuICAgICAgICAgICAgICAgICAgICAgICAgICBzZWFyY2hUZXJtc1tjb2wuaGVhZGVyTmFtZV0/Lmxlbmd0aCA+IDBcclxuICAgICAgICAgICAgICAgICAgICAgICAgICAgID8gXCJcIlxyXG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgOiBgU2VhcmNoICR7Y29sLmhlYWRlck5hbWV9Li4uYFxyXG4gICAgICAgICAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBtaW4tdy1bMzBweF0gYmctdHJhbnNwYXJlbnQgYm9yZGVyLW5vbmUgZm9jdXM6b3V0bGluZS1ub25lXCJcclxuICAgICAgICAgICAgICAgICAgICAgIC8+XHJcbiAgICAgICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgICAgIDwvZGl2PlxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgICAgKVxyXG4gICAgICAgICAgICApfVxyXG4gICAgICAgICAgPC8+XHJcbiAgICAgICAgKX1cclxuICAgICAgICB7ZmlsdGVyICYmIGZpbHRlckNvbHVtbnMubGVuZ3RoID4gMCAmJiAoXHJcbiAgICAgICAgICA8PlxyXG4gICAgICAgICAgICB7ZmlsdGVyQ29sdW1ucy5tYXAoKGNvbHVtbjogYW55LCBpbmRleDogbnVtYmVyKSA9PiB7XHJcbiAgICAgICAgICAgICAgPElucHV0XHJcbiAgICAgICAgICAgICAgICBrZXk9e2luZGV4fVxyXG4gICAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e1xyXG4gICAgICAgICAgICAgICAgICBmaWx0ZXIxUGxhY2VIb2xkZXIgPyBmaWx0ZXIxUGxhY2VIb2xkZXIgOiBmaWx0ZXJfY29sdW1uXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICB2YWx1ZT17aW5wdXRWYWx1ZXNbY29sdW1uLmhlYWRlck5hbWVdIHx8IFwiXCJ9XHJcbiAgICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+XHJcbiAgICAgICAgICAgICAgICAgIHNldElucHV0VmFsdWVzKHtcclxuICAgICAgICAgICAgICAgICAgICAuLi5pbnB1dFZhbHVlcyxcclxuICAgICAgICAgICAgICAgICAgICBbY29sdW1uLmhlYWRlck5hbWVdOiBlLnRhcmdldC52YWx1ZSxcclxuICAgICAgICAgICAgICAgICAgfSlcclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgICAgIG9uS2V5RG93bj17KGUpID0+IGhhbmRsZUtleURvd24oZSwgY29sdW1uLmhlYWRlck5hbWUpfVxyXG4gICAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy1bMjAlXSBkYXJrOmJnLWdyYXktNzAwICFvdXRsaW5lLW1haW4tY29sb3JcIlxyXG4gICAgICAgICAgICAgIC8+O1xyXG4gICAgICAgICAgICB9KX1cclxuICAgICAgICAgIDwvPlxyXG4gICAgICAgICl9XHJcbiAgICAgICAge2ZpbHRlcjIgJiYgKFxyXG4gICAgICAgICAgPD5cclxuICAgICAgICAgICAge2ZpbHRlckNvbHVtbnMubWFwKChjb2x1bW46IGFueSwgaW5kZXg6IG51bWJlcikgPT4ge1xyXG4gICAgICAgICAgICAgIDxJbnB1dFxyXG4gICAgICAgICAgICAgICAga2V5PXtpbmRleH1cclxuICAgICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXtgJHtmaWx0ZXJfY29sdW1uMn1gfVxyXG4gICAgICAgICAgICAgICAgdmFsdWU9e2lucHV0VmFsdWVzW2NvbHVtbi5oZWFkZXJOYW1lXSB8fCBcIlwifVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PlxyXG4gICAgICAgICAgICAgICAgICBzZXRJbnB1dFZhbHVlcyh7XHJcbiAgICAgICAgICAgICAgICAgICAgLi4uaW5wdXRWYWx1ZXMsXHJcbiAgICAgICAgICAgICAgICAgICAgW2NvbHVtbi5oZWFkZXJOYW1lXTogZS50YXJnZXQudmFsdWUsXHJcbiAgICAgICAgICAgICAgICAgIH0pXHJcbiAgICAgICAgICAgICAgICB9XHJcbiAgICAgICAgICAgICAgICBvbktleURvd249eyhlKSA9PiBoYW5kbGVLZXlEb3duKGUsIGNvbHVtbi5oZWFkZXJOYW1lKX1cclxuICAgICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctWzIwJV0gZGFyazpiZy1ncmF5LTcwMCAhb3V0bGluZS1tYWluLWNvbG9yXCJcclxuICAgICAgICAgICAgICAvPjtcclxuICAgICAgICAgICAgfSl9XHJcbiAgICAgICAgICA8Lz5cclxuICAgICAgICApfVxyXG4gICAgICA8L2Rpdj5cclxuXHJcbiAgICAgIHt0b3RhbCAmJiA8ZGl2Pnt0b3RhbHZpZXd9PC9kaXY+fVxyXG5cclxuICAgICAgPGRpdiBjbGFzc05hbWU9e2NuKFwiXCIsIGNsYXNzTmFtZSl9PlxyXG4gICAgICAgIDxkaXZcclxuICAgICAgICAgIGNsYXNzTmFtZT1cImFnLXRoZW1lLWFscGluZSBjdXN0b20tYWctZ3JpZFwiXHJcbiAgICAgICAgICBzdHlsZT17eyBoZWlnaHQ6IFwiMTAwdmhcIiwgd2lkdGg6IFwiMTAwJVwiIH19XHJcbiAgICAgICAgPlxyXG4gICAgICAgICAgPEFnR3JpZFJlYWN0XHJcbiAgICAgICAgICAgIHJlZj17Z3JpZFJlZn1cclxuICAgICAgICAgICAgcm93RGF0YT17cHJvY2Vzc2VkRGF0YX1cclxuICAgICAgICAgICAgY29sdW1uRGVmcz17Y29sdW1uc1dpdGhTZXJpYWxOdW1iZXJ9XHJcbiAgICAgICAgICAgIGhlYWRlckhlaWdodD17MzV9XHJcbiAgICAgICAgICAgIG92ZXJsYXlOb1Jvd3NUZW1wbGF0ZT17bm9Sb3dzT3ZlcmxheVRlbXBsYXRlfVxyXG4gICAgICAgICAgICBvbkdyaWRSZWFkeT17KHBhcmFtcykgPT4ge1xyXG4gICAgICAgICAgICAgIHBhcmFtcy5hcGkuc2l6ZUNvbHVtbnNUb0ZpdCgpO1xyXG4gICAgICAgICAgICAgIC8vIFNob3cgb3ZlcmxheXMgb24gZ3JpZCByZWFkeVxyXG4gICAgICAgICAgICAgIGlmIChpc0xvYWRpbmcpIHtcclxuICAgICAgICAgICAgICAgIHBhcmFtcy5hcGkuc2hvd0xvYWRpbmdPdmVybGF5KCk7XHJcbiAgICAgICAgICAgICAgfSBlbHNlIGlmICghcHJvY2Vzc2VkRGF0YSB8fCBwcm9jZXNzZWREYXRhLmxlbmd0aCA9PT0gMCkge1xyXG4gICAgICAgICAgICAgICAgcGFyYW1zLmFwaS5zaG93Tm9Sb3dzT3ZlcmxheSgpO1xyXG4gICAgICAgICAgICAgIH0gZWxzZSB7XHJcbiAgICAgICAgICAgICAgICBwYXJhbXMuYXBpLmhpZGVPdmVybGF5KCk7XHJcbiAgICAgICAgICAgICAgfVxyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgICBvbkZpcnN0RGF0YVJlbmRlcmVkPXsocGFyYW1zKSA9PiB7XHJcbiAgICAgICAgICAgICAgcGFyYW1zLmFwaS5zaXplQ29sdW1uc1RvRml0KCk7XHJcbiAgICAgICAgICAgIH19XHJcbiAgICAgICAgICAgIG9uQ29sdW1uVmlzaWJsZT17KGV2ZW50KSA9PiB7XHJcbiAgICAgICAgICAgICAgZXZlbnQuYXBpLnNpemVDb2x1bW5zVG9GaXQoKTtcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgb25HcmlkU2l6ZUNoYW5nZWQ9eyhwYXJhbXMpID0+IHtcclxuICAgICAgICAgICAgICBwYXJhbXMuYXBpLnNpemVDb2x1bW5zVG9GaXQoKTtcclxuICAgICAgICAgICAgfX1cclxuICAgICAgICAgICAgZGVmYXVsdENvbERlZj17e1xyXG4gICAgICAgICAgICAgIHNvcnRhYmxlOiB0cnVlLFxyXG4gICAgICAgICAgICAgIHJlc2l6YWJsZTogdHJ1ZSxcclxuICAgICAgICAgICAgICBjZWxsU3R5bGU6IHsgYm9yZGVyUmlnaHQ6IFwiMXB4IHNvbGlkICNkZGRcIiB9LFxyXG4gICAgICAgICAgICB9fVxyXG4gICAgICAgICAgLz5cclxuICAgICAgICA8L2Rpdj5cclxuICAgICAgPC9kaXY+XHJcblxyXG4gICAgICB7ZGF0YSAmJiAoXHJcbiAgICAgICAgPFBhZ2luYXRpb25cclxuICAgICAgICAgIGN1cnJlbnRQYWdlPXtcclxuICAgICAgICAgICAgdG90YWxQYWdlc1xyXG4gICAgICAgICAgICAgID8gTnVtYmVyKHBhcmFtcy5nZXQoXCJwYWdlXCIpKSB8fCAxXHJcbiAgICAgICAgICAgICAgOiAoZ3JpZFJlZi5jdXJyZW50Py5hcGk/LnBhZ2luYXRpb25HZXRDdXJyZW50UGFnZSgpIHx8IDApICsgMVxyXG4gICAgICAgICAgfVxyXG4gICAgICAgICAgdG90YWxQYWdlcz17XHJcbiAgICAgICAgICAgIHRvdGFsUGFnZXNcclxuICAgICAgICAgICAgICA/IHRvdGFsUGFnZXNcclxuICAgICAgICAgICAgICA6IGdyaWRSZWYuY3VycmVudD8uYXBpPy5wYWdpbmF0aW9uR2V0VG90YWxQYWdlcygpIHx8IDFcclxuICAgICAgICAgIH1cclxuICAgICAgICAgIG9uUGFnZUNoYW5nZT17KHBhZ2U6IG51bWJlcikgPT4ge1xyXG4gICAgICAgICAgICBpZiAoZ3JpZFJlZi5jdXJyZW50KSB7XHJcbiAgICAgICAgICAgICAgZ3JpZFJlZj8uY3VycmVudD8uYXBpPy5wYWdpbmF0aW9uR29Ub1BhZ2UocGFnZSAtIDEpO1xyXG4gICAgICAgICAgICB9XHJcblxyXG4gICAgICAgICAgICBpZiAodG90YWxQYWdlcykge1xyXG4gICAgICAgICAgICAgIHBhcmFtcy5zZXQoXCJwYWdlXCIsIHBhZ2UudG9TdHJpbmcoKSk7XHJcbiAgICAgICAgICAgICAgcmVwbGFjZShgJHtwYXRobmFtZX0/JHtwYXJhbXMudG9TdHJpbmcoKX1gKTtcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgICAgfX1cclxuICAgICAgICAvPlxyXG4gICAgICApfVxyXG4gICAgPC9kaXY+XHJcbiAgKTtcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IERhdGFHcmlkVGFibGU7XHJcbiJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlRWZmZWN0IiwidXNlUmVmIiwidXNlQ29udGV4dCIsInVzZU1lbW8iLCJGYUZpbHRlciIsIkZhU2VhcmNoIiwidXNlUm91dGVyIiwidXNlU2VhcmNoUGFyYW1zIiwidXNlUGF0aG5hbWUiLCJEcm9wZG93bk1lbnUiLCJEcm9wZG93bk1lbnVDaGVja2JveEl0ZW0iLCJEcm9wZG93bk1lbnVDb250ZW50IiwiRHJvcGRvd25NZW51VHJpZ2dlciIsIklucHV0IiwiUGFnaW5hdGlvbiIsIkFnR3JpZFJlYWN0IiwiQWxsQ29tbXVuaXR5TW9kdWxlIiwiTW9kdWxlUmVnaXN0cnkiLCJXb3JrUmVwb3J0Q29udGV4dCIsIlRyYWNrZXJDb250ZXh0IiwiY24iLCJDdXN0b21JbnB1dCIsInJlZ2lzdGVyTW9kdWxlcyIsIkRhdGFHcmlkVGFibGUiLCJjb2x1bW5zIiwiZGF0YSIsImlzTG9hZGluZyIsInNob3dDb2xEcm9wRG93bnMiLCJmaWx0ZXIiLCJmaWx0ZXIyIiwiZmlsdGVyMyIsImZpbHRlcjN2aWV3IiwidG90YWwiLCJ0b3RhbHZpZXciLCJmaWx0ZXJfY29sdW1uIiwiZmlsdGVyX2NvbHVtbjIiLCJmaWx0ZXJfY29sdW1uMyIsImZpbHRlcl9jb2x1bW40IiwidG90YWxQYWdlcyIsInNob3dQYWdlRW50cmllcyIsImNsYXNzTmFtZSIsImZpbHRlcjFQbGFjZUhvbGRlciIsInNob3dTZWFyY2hDb2x1bW4iLCJwYWdlU2l6ZSIsImlzVGltZXJSdW5uaW5nIiwic2V0SXNUaW1lclJ1bm5pbmciLCJvbkZpbHRlcmVkRGF0YUNoYW5nZSIsImdyaWRSZWYiLCJwYWdlIiwic2V0UGFnZSIsInNlbGVjdGVkQ29sdW1ucyIsInNldFNlbGVjdGVkQ29sdW1ucyIsInNlYXJjaFBhcmFtcyIsInBhcmFtcyIsIlVSTFNlYXJjaFBhcmFtcyIsInBhdGhuYW1lIiwicmVwbGFjZSIsImN1cnJlbnRQYWdlIiwiTnVtYmVyIiwiZ2V0IiwiY3VycmVudFBhZ2VTaXplIiwicGFnaW5hdGlvbiIsInNldFBhZ2luYXRpb24iLCJwYWdlSW5kZXgiLCJzZXRGcm9tRGF0ZSIsImZyb21EYXRlIiwidG9EYXRlIiwic2V0VG9EYXRlIiwic2V0RkRhdGUiLCJmRGF0ZSIsInREYXRlIiwic2V0VERhdGUiLCJjb2x1bW5TdGF0ZSIsInNldENvbHVtblN0YXRlIiwiY29sdW1uRGF0YSIsInNldENvbHVtbkRhdGEiLCJjb2x1bW5WaXNpYmlsaXR5Iiwic2V0Q29sdW1uVmlzaWJpbGl0eSIsImlucHV0VmFsdWVzIiwic2V0SW5wdXRWYWx1ZXMiLCJzZWFyY2hUZXJtcyIsInNldFNlYXJjaFRlcm1zIiwicHJvY2Vzc2VkRGF0YSIsIm1hcCIsIml0ZW0iLCJpbmRleCIsInN0YWJsZUlkIiwiY29sdW1uc1dpdGhTZXJpYWxOdW1iZXIiLCJoZWFkZXJOYW1lIiwiZmllbGQiLCJzb3J0YWJsZSIsIndpZHRoIiwibWluV2lkdGgiLCJtYXhXaWR0aCIsImNlbGxTdHlsZSIsInRleHRBbGlnbiIsInBpbm5lZCIsImNvbXBhcmF0b3IiLCJ2YWx1ZUEiLCJ2YWx1ZUIiLCJjb2wiLCJpbml0aWFsVmlzaWJpbGl0eSIsImZvckVhY2giLCJjdXJyZW50IiwiYXBpIiwic2hvd0xvYWRpbmdPdmVybGF5IiwibGVuZ3RoIiwic2hvd05vUm93c092ZXJsYXkiLCJoaWRlT3ZlcmxheSIsImhhbmRsZVBhZ2VDaGFuZ2UiLCJlIiwibmV3UGFnZVNpemUiLCJwYXJzZUludCIsInRhcmdldCIsInZhbHVlIiwic2V0IiwidG9TdHJpbmciLCJwcmV2U3RhdGUiLCJoYW5kbGVDb2x1bW5TZWxlY3Rpb24iLCJjb2x1bW5LZXkiLCJjb2x1bW5IZWFkZXIiLCJwcmV2U2VsZWN0ZWRDb2x1bW5zIiwidXBkYXRlZENvbHVtbnMiLCJpbmNsdWRlcyIsInVwZGF0ZWRQYXJhbXMiLCJkZWxldGUiLCJoYW5kbGVTZWFyY2hDaGFuZ2UiLCJldmVudCIsImNvbHVtbk5hbWUiLCJ0cmltIiwiaGFuZGxlS2V5RG93biIsImN1cnJlbnRJbnB1dCIsImtleSIsInByZXZlbnREZWZhdWx0IiwidXBkYXRlZCIsInVwZGF0ZVNlYXJjaFBhcmFtcyIsInBvcCIsImhhbmRsZVJlbW92ZVRlcm0iLCJ0ZXJtIiwidCIsInRlcm1zIiwiam9pbiIsImZpbHRlckNvbHVtbnMiLCJmaW5kIiwidG9nZ2xlQ29sdW1uVmlzaWJpbGl0eSIsImlzVmlzaWJsZSIsInByZXYiLCJzZXRDb2x1bW5zVmlzaWJsZSIsIm5vUm93c092ZXJsYXlUZW1wbGF0ZSIsImRpdiIsInNlbGVjdCIsIm9uQ2hhbmdlIiwib3B0aW9uIiwiYXNDaGlsZCIsInAiLCJvblNlbGVjdCIsImNvbHVtbiIsImhpZGVhYmxlIiwiY2hlY2tlZCIsIm9uQ2hlY2tlZENoYW5nZSIsImlkIiwiaSIsInNwYW4iLCJidXR0b24iLCJvbkNsaWNrIiwib25LZXlEb3duIiwicGxhY2Vob2xkZXIiLCJzdHlsZSIsImhlaWdodCIsInJlZiIsInJvd0RhdGEiLCJjb2x1bW5EZWZzIiwiaGVhZGVySGVpZ2h0Iiwib3ZlcmxheU5vUm93c1RlbXBsYXRlIiwib25HcmlkUmVhZHkiLCJzaXplQ29sdW1uc1RvRml0Iiwib25GaXJzdERhdGFSZW5kZXJlZCIsIm9uQ29sdW1uVmlzaWJsZSIsIm9uR3JpZFNpemVDaGFuZ2VkIiwiZGVmYXVsdENvbERlZiIsInJlc2l6YWJsZSIsImJvcmRlclJpZ2h0IiwicGFnaW5hdGlvbkdldEN1cnJlbnRQYWdlIiwicGFnaW5hdGlvbkdldFRvdGFsUGFnZXMiLCJvblBhZ2VDaGFuZ2UiLCJwYWdpbmF0aW9uR29Ub1BhZ2UiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/_component/DataGridTable.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/user/tracker/column.tsx":
/*!*************************************!*\
  !*** ./app/user/tracker/column.tsx ***!
  \*************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_swrFetching__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/swrFetching */ \"(app-pages-browser)/./lib/swrFetching.ts\");\n/* harmony import */ var _UpdateTracker__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./UpdateTracker */ \"(app-pages-browser)/./app/user/tracker/UpdateTracker.tsx\");\n/* harmony import */ var _lib_permissionWrapper__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/permissionWrapper */ \"(app-pages-browser)/./lib/permissionWrapper.ts\");\n/* harmony import */ var luxon__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! luxon */ \"(app-pages-browser)/./node_modules/luxon/src/luxon.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\nconst Column = (isTimerRunning, setElapsedTime, setIsTimerRunning, setPreviousSelectedClient, setPreviousSelectedCarrier, permissions)=>{\n    const columns = [\n        {\n            field: \"date\",\n            headerName: \"Date\",\n            width: 100,\n            minWidth: 100,\n            valueFormatter: (param)=>{\n                let { value } = param;\n                if (!value) return \"-\";\n                return luxon__WEBPACK_IMPORTED_MODULE_5__.DateTime.fromISO(value, {\n                    zone: \"utc\"\n                }).toFormat(\"dd-MM-yyyy\");\n            },\n            filter: \"agDateColumnFilter\",\n            filterParams: {\n                comparator: (filterLocalDateAtMidnight, cellValue)=>{\n                    if (!cellValue) return -1;\n                    const cellDate = luxon__WEBPACK_IMPORTED_MODULE_5__.DateTime.fromISO(cellValue, {\n                        zone: \"utc\"\n                    }).startOf(\"day\").toJSDate();\n                    const filterDate = new Date(Date.UTC(filterLocalDateAtMidnight.getFullYear(), filterLocalDateAtMidnight.getMonth(), filterLocalDateAtMidnight.getDate()));\n                    if (cellDate < filterDate) return -1;\n                    if (cellDate > filterDate) return 1;\n                    return 0;\n                },\n                buttons: [\n                    \"clear\"\n                ]\n            }\n        },\n        {\n            field: \"username\",\n            headerName: \"Username\",\n            valueGetter: (param)=>{\n                let { data } = param;\n                var _data_user;\n                return (data === null || data === void 0 ? void 0 : (_data_user = data.user) === null || _data_user === void 0 ? void 0 : _data_user.username) || \"-\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            }\n        },\n        {\n            field: \"clientname\",\n            headerName: \"Client\",\n            valueGetter: (param)=>{\n                let { data } = param;\n                var _data_client;\n                return (data === null || data === void 0 ? void 0 : (_data_client = data.client) === null || _data_client === void 0 ? void 0 : _data_client.client_name) || \"-\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            }\n        },\n        {\n            field: \"carriername\",\n            headerName: \"Carrier\",\n            valueGetter: (param)=>{\n                let { data } = param;\n                var _data_carrier;\n                return (data === null || data === void 0 ? void 0 : data.carrier_id) === null ? \"N/A\" : (data === null || data === void 0 ? void 0 : (_data_carrier = data.carrier) === null || _data_carrier === void 0 ? void 0 : _data_carrier.name) || \"-\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            }\n        },\n        {\n            field: \"work_type\",\n            headerName: \"Work Type\",\n            valueGetter: (param)=>{\n                let { data } = param;\n                var _data_work_type;\n                return (data === null || data === void 0 ? void 0 : (_data_work_type = data.work_type) === null || _data_work_type === void 0 ? void 0 : _data_work_type.work_type) || \"-\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            }\n        },\n        {\n            field: \"category\",\n            headerName: \"Category\",\n            valueGetter: (param)=>{\n                let { data } = param;\n                var _data_category;\n                return (data === null || data === void 0 ? void 0 : (_data_category = data.category) === null || _data_category === void 0 ? void 0 : _data_category.category_name) || \"-\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            }\n        },\n        {\n            field: \"task_type\",\n            headerName: \"Type\",\n            valueGetter: (param)=>{\n                let { data } = param;\n                return (data === null || data === void 0 ? void 0 : data.task_type) || \"-\";\n            },\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            }\n        },\n        {\n            field: \"start_time\",\n            headerName: \"Start Time\",\n            // valueFormatter: ({ value }) => {\n            //   if (!value) return \"-\";\n            //   const date = new Date(value);\n            //   return date.toLocaleTimeString(\"en-US\", {\n            //     hour: \"2-digit\",\n            //     minute: \"2-digit\",\n            //     hour12: true,\n            //   });\n            // },\n            valueGetter: (params)=>{\n                var _params_data;\n                return (0,_lib_swrFetching__WEBPACK_IMPORTED_MODULE_2__.formatTimeZone)(((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.start_time) || \"\");\n            },\n            cellStyle: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                textAlign: \"center\"\n            }\n        },\n        {\n            field: \"finish_time\",\n            headerName: \"Finish Time\",\n            // valueFormatter: ({ value }) => {\n            //   if (!value) return \"-\";\n            //   const date = new Date(value);\n            //   return date.toLocaleTimeString(\"en-US\", {\n            //     hour: \"2-digit\",\n            //     minute: \"2-digit\",\n            //     hour12: true,\n            //   });\n            // },\n            valueGetter: (params)=>{\n                var _params_data;\n                return (0,_lib_swrFetching__WEBPACK_IMPORTED_MODULE_2__.formatTimeZone)(((_params_data = params.data) === null || _params_data === void 0 ? void 0 : _params_data.finish_time) || \"\");\n            },\n            cellStyle: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                textAlign: \"center\"\n            }\n        },\n        // {\n        //   accessorKey: \"pause\",\n        //   header: \"History\",\n        //   cell: ({ row }) => {\n        //     return (\n        //       <>\n        //         <PauseResumeHistory data={row.original} />\n        //       </>\n        //     );\n        //   },\n        // },\n        // {\n        //   accessorKey: \"work_status\",\n        //   header: \"Workstatus\",\n        //   cell: ({ row }) => {\n        //     const handleResumeTask = async (\n        //       workReportId: number,\n        //       isTimerRunning: boolean\n        //     ) => {\n        //       if (!isTimerRunning) {\n        //         localStorage.removeItem(\"timerData\");\n        //         const response = await formSubmit(\n        //           `${workreport_routes.UPDATE_WORKREPORT}/${workReportId}`,\n        //           \"PUT\",\n        //           {\n        //             action: \"resume\",\n        //             work_status: \"RESUMED\",\n        //           },\n        //           \"/user/tracker\"\n        //         );\n        //         if (response.success) {\n        //           const time =\n        //             row.original.time_spent &&\n        //             subtractTime(row.original.time_spent);\n        //           setIsTimerRunning(true);\n        //           setElapsedTime((prev) => {\n        //             const updatedTime = prev + 1;\n        //             storeData(\"timerData\", {\n        //               startTime: time,\n        //               elapsedTime: updatedTime,\n        //             });\n        //             return updatedTime;\n        //           });\n        //           setPreviousSelectedClient(row.original.client);\n        //           setPreviousSelectedCarrier(row.original.carrier);\n        //           localStorage.setItem(\n        //             \"workType\",\n        //             JSON.stringify(parseInt(row.original.work_type.id))\n        //           );\n        //           localStorage.setItem(\n        //             \"client\",\n        //             JSON.stringify(row.original.client)\n        //           );\n        //           localStorage.setItem(\n        //             \"carrier\",\n        //             JSON.stringify(row.original.carrier)\n        //           );\n        //           router.refresh();\n        //         }\n        //       } else {\n        //         toast.error(\"Timer is running. Pause or stop it first.\");\n        //       }\n        //     };\n        //     const work_status = row.original.work_status;\n        //     if (work_status === \"PAUSED\") {\n        //       return (\n        //         <Badge\n        //           // onClick={()=>{ ('onclick')}}\n        //           onClick={() => handleResumeTask(row.original.id, isTimerRunning)}\n        //           className=\" cursor-pointer text-center w-20  flex items-center text-white bg-orange-500 hover:bg-orange-600 \"\n        //         >\n        //           {}\n        //           PAUSED\n        //           {/* <FaPlay className=\"text-sm \" /> */}\n        //         </Badge>\n        //       );\n        //     }\n        //     return (\n        //       <Badge\n        //         className={`cursor-pointer flex items-center gap-2 text-center w-20  justify-center text-white ${\n        //           work_status === \"FINISHED\"\n        //             ? \"bg-gray-500\"\n        //             : work_status === \"RESUMED\"\n        //             ? \"bg-blue-500\"\n        //             : \"bg-green-500\"\n        //         } cursor-pointer`}\n        //       >\n        //         {work_status}\n        //       </Badge>\n        //     );\n        //   },\n        // },\n        {\n            field: \"time_spent\",\n            headerName: \"Time Spent\",\n            valueGetter: (param)=>{\n                let { data } = param;\n                const timeSpent = data === null || data === void 0 ? void 0 : data.time_spent;\n                if (!timeSpent) return \"-\";\n                const formatted = (0,_lib_swrFetching__WEBPACK_IMPORTED_MODULE_2__.formatDuration)(timeSpent);\n                const [hours, minutes] = formatted.split(\":\");\n                return \"\".concat(hours, \":\").concat(minutes);\n            },\n            cellStyle: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                textAlign: \"center\"\n            }\n        },\n        {\n            field: \"actual_number\",\n            headerName: \"Actual No\",\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            },\n            cellStyle: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                textAlign: \"center\"\n            }\n        },\n        {\n            field: \"notes\",\n            headerName: \"Notes\",\n            filter: \"agTextColumnFilter\",\n            filterParams: {\n                buttons: [\n                    \"clear\"\n                ]\n            }\n        },\n        {\n            field: \"action\",\n            headerName: \"Action\",\n            cellRenderer: (params)=>{\n                const workReport = params === null || params === void 0 ? void 0 : params.data;\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center gap-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_permissionWrapper__WEBPACK_IMPORTED_MODULE_4__.PermissionWrapper, {\n                        permissions: permissions,\n                        requiredPermissions: [\n                            \"update-tracker\"\n                        ],\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_UpdateTracker__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            workReport: workReport\n                        }, void 0, false, {\n                            fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\tracker\\\\column.tsx\",\n                            lineNumber: 338,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\tracker\\\\column.tsx\",\n                        lineNumber: 334,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\app\\\\user\\\\tracker\\\\column.tsx\",\n                    lineNumber: 333,\n                    columnNumber: 11\n                }, undefined);\n            },\n            sortable: false,\n            cellStyle: {\n                display: \"flex\",\n                justifyContent: \"center\",\n                alignItems: \"center\",\n                textAlign: \"center\"\n            }\n        }\n    ];\n    return columns;\n};\n_c = Column;\n/* harmony default export */ __webpack_exports__[\"default\"] = (Column);\nvar _c;\n$RefreshReg$(_c, \"Column\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC91c2VyL3RyYWNrZXIvY29sdW1uLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFDb0Q7QUFTekI7QUFhaUI7QUFFZ0I7QUFDM0I7QUF1QmpDLE1BQU1NLFNBQVMsQ0FDYkMsZ0JBQ0FDLGdCQUNBQyxtQkFDQUMsMkJBQ0FDLDRCQUNBQztJQUVBLE1BQU1DLFVBQVU7UUFDZDtZQUNFQyxPQUFPO1lBQ1BDLFlBQVk7WUFDWkMsT0FBTztZQUNQQyxVQUFVO1lBQ1ZDLGdCQUFnQjtvQkFBQyxFQUFFQyxLQUFLLEVBQUU7Z0JBQ3hCLElBQUksQ0FBQ0EsT0FBTyxPQUFPO2dCQUNuQixPQUFPZCwyQ0FBUUEsQ0FBQ2UsT0FBTyxDQUFDRCxPQUFPO29CQUFFRSxNQUFNO2dCQUFNLEdBQUdDLFFBQVEsQ0FBQztZQUMzRDtZQUNBQyxRQUFRO1lBQ1JDLGNBQWM7Z0JBQ1pDLFlBQVksQ0FBQ0MsMkJBQWlDQztvQkFDNUMsSUFBSSxDQUFDQSxXQUFXLE9BQU8sQ0FBQztvQkFFeEIsTUFBTUMsV0FBV3ZCLDJDQUFRQSxDQUFDZSxPQUFPLENBQUNPLFdBQVc7d0JBQUVOLE1BQU07b0JBQU0sR0FDeERRLE9BQU8sQ0FBQyxPQUNSQyxRQUFRO29CQUNYLE1BQU1DLGFBQWEsSUFBSUMsS0FDckJBLEtBQUtDLEdBQUcsQ0FDTlAsMEJBQTBCUSxXQUFXLElBQ3JDUiwwQkFBMEJTLFFBQVEsSUFDbENULDBCQUEwQlUsT0FBTztvQkFJckMsSUFBSVIsV0FBV0csWUFBWSxPQUFPLENBQUM7b0JBQ25DLElBQUlILFdBQVdHLFlBQVksT0FBTztvQkFDbEMsT0FBTztnQkFDVDtnQkFDQU0sU0FBUztvQkFBQztpQkFBUTtZQUNwQjtRQUNGO1FBQ0E7WUFDRXZCLE9BQU87WUFDUEMsWUFBWTtZQUNadUIsYUFBYTtvQkFBQyxFQUFFQyxJQUFJLEVBQUU7b0JBQUtBO3VCQUFBQSxDQUFBQSxpQkFBQUEsNEJBQUFBLGFBQUFBLEtBQU1DLElBQUksY0FBVkQsaUNBQUFBLFdBQVlFLFFBQVEsS0FBSTs7WUFDbkRsQixRQUFRO1lBQ1JDLGNBQWM7Z0JBQ1phLFNBQVM7b0JBQUM7aUJBQVE7WUFDcEI7UUFDRjtRQUNBO1lBQ0V2QixPQUFPO1lBQ1BDLFlBQVk7WUFDWnVCLGFBQWE7b0JBQUMsRUFBRUMsSUFBSSxFQUFFO29CQUFLQTt1QkFBQUEsQ0FBQUEsaUJBQUFBLDRCQUFBQSxlQUFBQSxLQUFNRyxNQUFNLGNBQVpILG1DQUFBQSxhQUFjSSxXQUFXLEtBQUk7O1lBQ3hEcEIsUUFBUTtZQUNSQyxjQUFjO2dCQUNaYSxTQUFTO29CQUFDO2lCQUFRO1lBQ3BCO1FBQ0Y7UUFDQTtZQUNFdkIsT0FBTztZQUNQQyxZQUFZO1lBQ1p1QixhQUFhO29CQUFDLEVBQUVDLElBQUksRUFBRTtvQkFDZ0JBO3VCQUFwQ0EsQ0FBQUEsaUJBQUFBLDJCQUFBQSxLQUFNSyxVQUFVLE1BQUssT0FBTyxRQUFRTCxDQUFBQSxpQkFBQUEsNEJBQUFBLGdCQUFBQSxLQUFNTSxPQUFPLGNBQWJOLG9DQUFBQSxjQUFlTyxJQUFJLEtBQUk7O1lBQzdEdkIsUUFBUTtZQUNSQyxjQUFjO2dCQUNaYSxTQUFTO29CQUFDO2lCQUFRO1lBQ3BCO1FBQ0Y7UUFDQTtZQUNFdkIsT0FBTztZQUNQQyxZQUFZO1lBQ1p1QixhQUFhO29CQUFDLEVBQUVDLElBQUksRUFBRTtvQkFBS0E7dUJBQUFBLENBQUFBLGlCQUFBQSw0QkFBQUEsa0JBQUFBLEtBQU1RLFNBQVMsY0FBZlIsc0NBQUFBLGdCQUFpQlEsU0FBUyxLQUFJOztZQUN6RHhCLFFBQVE7WUFDUkMsY0FBYztnQkFDWmEsU0FBUztvQkFBQztpQkFBUTtZQUNwQjtRQUNGO1FBQ0E7WUFDRXZCLE9BQU87WUFDUEMsWUFBWTtZQUNadUIsYUFBYTtvQkFBQyxFQUFFQyxJQUFJLEVBQUU7b0JBQUtBO3VCQUFBQSxDQUFBQSxpQkFBQUEsNEJBQUFBLGlCQUFBQSxLQUFNUyxRQUFRLGNBQWRULHFDQUFBQSxlQUFnQlUsYUFBYSxLQUFJOztZQUM1RDFCLFFBQVE7WUFDUkMsY0FBYztnQkFDWmEsU0FBUztvQkFBQztpQkFBUTtZQUNwQjtRQUNGO1FBQ0E7WUFDRXZCLE9BQU87WUFDUEMsWUFBWTtZQUNadUIsYUFBYTtvQkFBQyxFQUFFQyxJQUFJLEVBQUU7dUJBQUtBLENBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTVcsU0FBUyxLQUFJOztZQUM5QzNCLFFBQVE7WUFDUkMsY0FBYztnQkFDWmEsU0FBUztvQkFBQztpQkFBUTtZQUNwQjtRQUNGO1FBQ0E7WUFDRXZCLE9BQU87WUFDUEMsWUFBWTtZQUNaLG1DQUFtQztZQUNuQyw0QkFBNEI7WUFDNUIsa0NBQWtDO1lBQ2xDLDhDQUE4QztZQUM5Qyx1QkFBdUI7WUFDdkIseUJBQXlCO1lBQ3pCLG9CQUFvQjtZQUNwQixRQUFRO1lBQ1IsS0FBSztZQUNMdUIsYUFBYSxDQUFDYTtvQkFBMEJBO3VCQUFmakQsZ0VBQWNBLENBQUNpRCxFQUFBQSxlQUFBQSxPQUFPWixJQUFJLGNBQVhZLG1DQUFBQSxhQUFhQyxVQUFVLEtBQUk7O1lBQ25FQyxXQUFXO2dCQUNUQyxTQUFTO2dCQUNUQyxnQkFBZ0I7Z0JBQ2hCQyxZQUFZO2dCQUNaQyxXQUFXO1lBQ2I7UUFDRjtRQUNBO1lBQ0UzQyxPQUFPO1lBQ1BDLFlBQVk7WUFDWixtQ0FBbUM7WUFDbkMsNEJBQTRCO1lBQzVCLGtDQUFrQztZQUNsQyw4Q0FBOEM7WUFDOUMsdUJBQXVCO1lBQ3ZCLHlCQUF5QjtZQUN6QixvQkFBb0I7WUFDcEIsUUFBUTtZQUNSLEtBQUs7WUFDTHVCLGFBQWEsQ0FBQ2E7b0JBQTBCQTt1QkFBZmpELGdFQUFjQSxDQUFDaUQsRUFBQUEsZUFBQUEsT0FBT1osSUFBSSxjQUFYWSxtQ0FBQUEsYUFBYU8sV0FBVyxLQUFJOztZQUNwRUwsV0FBVztnQkFDVEMsU0FBUztnQkFDVEMsZ0JBQWdCO2dCQUNoQkMsWUFBWTtnQkFDWkMsV0FBVztZQUNiO1FBQ0Y7UUFDQSxJQUFJO1FBQ0osMEJBQTBCO1FBQzFCLHVCQUF1QjtRQUN2Qix5QkFBeUI7UUFDekIsZUFBZTtRQUNmLFdBQVc7UUFDWCxxREFBcUQ7UUFDckQsWUFBWTtRQUNaLFNBQVM7UUFDVCxPQUFPO1FBQ1AsS0FBSztRQUNMLElBQUk7UUFDSixnQ0FBZ0M7UUFDaEMsMEJBQTBCO1FBQzFCLHlCQUF5QjtRQUN6Qix1Q0FBdUM7UUFDdkMsOEJBQThCO1FBRTlCLGdDQUFnQztRQUNoQyxhQUFhO1FBQ2IsK0JBQStCO1FBQy9CLGdEQUFnRDtRQUVoRCw2Q0FBNkM7UUFDN0Msc0VBQXNFO1FBQ3RFLG1CQUFtQjtRQUVuQixjQUFjO1FBQ2QsZ0NBQWdDO1FBQ2hDLHNDQUFzQztRQUN0QyxlQUFlO1FBQ2YsNEJBQTRCO1FBQzVCLGFBQWE7UUFFYixrQ0FBa0M7UUFDbEMseUJBQXlCO1FBQ3pCLHlDQUF5QztRQUN6QyxxREFBcUQ7UUFFckQscUNBQXFDO1FBQ3JDLHVDQUF1QztRQUN2Qyw0Q0FBNEM7UUFDNUMsdUNBQXVDO1FBQ3ZDLGlDQUFpQztRQUNqQywwQ0FBMEM7UUFDMUMsa0JBQWtCO1FBRWxCLGtDQUFrQztRQUNsQyxnQkFBZ0I7UUFFaEIsNERBQTREO1FBQzVELDhEQUE4RDtRQUU5RCxrQ0FBa0M7UUFDbEMsMEJBQTBCO1FBQzFCLGtFQUFrRTtRQUNsRSxlQUFlO1FBQ2Ysa0NBQWtDO1FBQ2xDLHdCQUF3QjtRQUN4QixrREFBa0Q7UUFDbEQsZUFBZTtRQUNmLGtDQUFrQztRQUNsQyx5QkFBeUI7UUFDekIsbURBQW1EO1FBQ25ELGVBQWU7UUFFZiw4QkFBOEI7UUFDOUIsWUFBWTtRQUNaLGlCQUFpQjtRQUNqQixvRUFBb0U7UUFDcEUsVUFBVTtRQUNWLFNBQVM7UUFDVCxvREFBb0Q7UUFDcEQsc0NBQXNDO1FBQ3RDLGlCQUFpQjtRQUNqQixpQkFBaUI7UUFDakIsNENBQTRDO1FBQzVDLDhFQUE4RTtRQUM5RSwwSEFBMEg7UUFDMUgsWUFBWTtRQUNaLGVBQWU7UUFDZixtQkFBbUI7UUFDbkIsb0RBQW9EO1FBQ3BELG1CQUFtQjtRQUNuQixXQUFXO1FBQ1gsUUFBUTtRQUNSLGVBQWU7UUFDZixlQUFlO1FBQ2YsNEdBQTRHO1FBQzVHLHVDQUF1QztRQUN2Qyw4QkFBOEI7UUFDOUIsMENBQTBDO1FBQzFDLDhCQUE4QjtRQUM5QiwrQkFBK0I7UUFDL0IsNkJBQTZCO1FBQzdCLFVBQVU7UUFDVix3QkFBd0I7UUFDeEIsaUJBQWlCO1FBQ2pCLFNBQVM7UUFDVCxPQUFPO1FBQ1AsS0FBSztRQUVMO1lBQ0UzQyxPQUFPO1lBQ1BDLFlBQVk7WUFDWnVCLGFBQWE7b0JBQUMsRUFBRUMsSUFBSSxFQUFFO2dCQUNwQixNQUFNb0IsWUFBWXBCLGlCQUFBQSwyQkFBQUEsS0FBTXFCLFVBQVU7Z0JBQ2xDLElBQUksQ0FBQ0QsV0FBVyxPQUFPO2dCQUN2QixNQUFNRSxZQUFZNUQsZ0VBQWNBLENBQUMwRDtnQkFDakMsTUFBTSxDQUFDRyxPQUFPQyxRQUFRLEdBQUdGLFVBQVVHLEtBQUssQ0FBQztnQkFDekMsT0FBTyxHQUFZRCxPQUFURCxPQUFNLEtBQVcsT0FBUkM7WUFDckI7WUFDQVYsV0FBVztnQkFDVEMsU0FBUztnQkFDVEMsZ0JBQWdCO2dCQUNoQkMsWUFBWTtnQkFDWkMsV0FBVztZQUNiO1FBQ0Y7UUFDQTtZQUNFM0MsT0FBTztZQUNQQyxZQUFZO1lBQ1pRLFFBQVE7WUFDUkMsY0FBYztnQkFDWmEsU0FBUztvQkFBQztpQkFBUTtZQUNwQjtZQUNBZ0IsV0FBVztnQkFDVEMsU0FBUztnQkFDVEMsZ0JBQWdCO2dCQUNoQkMsWUFBWTtnQkFDWkMsV0FBVztZQUNiO1FBQ0Y7UUFDQTtZQUNFM0MsT0FBTztZQUNQQyxZQUFZO1lBQ1pRLFFBQVE7WUFDUkMsY0FBYztnQkFDWmEsU0FBUztvQkFBQztpQkFBUTtZQUNwQjtRQUNGO1FBQ0E7WUFDRXZCLE9BQU87WUFDUEMsWUFBWTtZQUNaa0QsY0FBYyxDQUFDZDtnQkFDYixNQUFNZSxhQUFhZixtQkFBQUEsNkJBQUFBLE9BQVFaLElBQUk7Z0JBQy9CLHFCQUNFLDhEQUFDNEI7b0JBQUlDLFdBQVU7OEJBQ2IsNEVBQUNoRSxxRUFBaUJBO3dCQUNoQlEsYUFBYUE7d0JBQ2J5RCxxQkFBcUI7NEJBQUM7eUJBQWlCO2tDQUV2Qyw0RUFBQ2xFLHNEQUFhQTs0QkFBQytELFlBQVlBOzs7Ozs7Ozs7Ozs7Ozs7O1lBSW5DO1lBQ0FJLFVBQVU7WUFDVmpCLFdBQVc7Z0JBQ1RDLFNBQVM7Z0JBQ1RDLGdCQUFnQjtnQkFDaEJDLFlBQVk7Z0JBQ1pDLFdBQVc7WUFDYjtRQUNGO0tBQ0Q7SUFDRCxPQUFPNUM7QUFDVDtLQS9TTVA7QUFpVE4sK0RBQWVBLE1BQU1BLEVBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL3VzZXIvdHJhY2tlci9jb2x1bW4udHN4PzVkOTUiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCI7XHJcbmltcG9ydCBSZWFjdCwgeyB1c2VDb250ZXh0LCB1c2VTdGF0ZSB9IGZyb20gXCJyZWFjdFwiO1xyXG5pbXBvcnQgeyBDb2x1bW5EZWYgfSBmcm9tIFwiQHRhbnN0YWNrL3JlYWN0LXRhYmxlXCI7XHJcbmltcG9ydCBEZWxldGVSb3cgZnJvbSBcIkAvYXBwL19jb21wb25lbnQvRGVsZXRlUm93XCI7XHJcbmltcG9ydCB7XHJcbiAgZm9ybWF0RGF0ZSxcclxuICBmb3JtYXREdXJhdGlvbixcclxuICBmb3JtYXRUaW1lWm9uZSxcclxuICBzdG9yZURhdGEsXHJcbiAgc3VidHJhY3RUaW1lLFxyXG59IGZyb20gXCJAL2xpYi9zd3JGZXRjaGluZ1wiO1xyXG5pbXBvcnQgeyBGYUhpc3RvcnksIEZhUGxheSB9IGZyb20gXCJyZWFjdC1pY29ucy9mYVwiO1xyXG5pbXBvcnQgeyBmb3JtU3VibWl0IH0gZnJvbSBcIkAvbGliL2hlbHBlcnNcIjtcclxuaW1wb3J0IHsgd29ya3JlcG9ydF9yb3V0ZXMgfSBmcm9tIFwiQC9saWIvcm91dGVQYXRoXCI7XHJcbmltcG9ydCB7IHVzZVJvdXRlciwgdXNlU2VhcmNoUGFyYW1zIH0gZnJvbSBcIm5leHQvbmF2aWdhdGlvblwiO1xyXG5pbXBvcnQgeyB1c2VQYXJlbnRDb250ZXh0IH0gZnJvbSBcIi4vUGFyZW50Q29udGV4dFwiO1xyXG5pbXBvcnQgeyByZXZhbGlkYXRlUGF0aCB9IGZyb20gXCJuZXh0L2NhY2hlXCI7XHJcbmltcG9ydCB7IEJhZGdlIH0gZnJvbSBcIkAvY29tcG9uZW50cy91aS9iYWRnZVwiO1xyXG5pbXBvcnQgeyBUcmFja2VyQ29udGV4dCB9IGZyb20gXCIuL1RyYWNrZXJDb250ZXh0XCI7XHJcbmltcG9ydCB7IHRvYXN0IH0gZnJvbSBcInNvbm5lclwiO1xyXG5pbXBvcnQgeyBEaWFsb2csIERpYWxvZ1RyaWdnZXIgfSBmcm9tIFwiQC9jb21wb25lbnRzL3VpL2RpYWxvZ1wiO1xyXG5pbXBvcnQgVHJpZ2dlckJ1dHRvbiBmcm9tIFwiQC9hcHAvX2NvbXBvbmVudC9UcmlnZ2VyQnV0dG9uXCI7XHJcbmltcG9ydCBQYXVzZVJlc3VtZUhpc3RvcnkgZnJvbSBcIi4vUGF1c2VSZXN1bWVIaXN0b3J5L1BhdXNlUmVzdW1lSGlzdG9yeVwiO1xyXG5pbXBvcnQgVXBkYXRlVHJhY2tlciBmcm9tIFwiLi9VcGRhdGVUcmFja2VyXCI7XHJcbmltcG9ydCB7IHBlcm1pc3Npb24gfSBmcm9tIFwicHJvY2Vzc1wiO1xyXG5pbXBvcnQgeyBQZXJtaXNzaW9uV3JhcHBlciB9IGZyb20gXCJAL2xpYi9wZXJtaXNzaW9uV3JhcHBlclwiO1xyXG5pbXBvcnQgeyBEYXRlVGltZSB9IGZyb20gXCJsdXhvblwiO1xyXG5pbXBvcnQgeyBDb2xEZWYgfSBmcm9tIFwiYWctZ3JpZC1jb21tdW5pdHlcIjtcclxuXHJcbmV4cG9ydCBpbnRlcmZhY2UgV29ya1JlcG9ydCB7XHJcbiAgbm90ZXM/OiBzdHJpbmc7XHJcbiAgZGF0ZTogRGF0ZTtcclxuICBzdGFydF90aW1lOiBEYXRlO1xyXG4gIGZpbmlzaF90aW1lPzogRGF0ZTtcclxuICB0aW1lX3NwZW50PzogbnVtYmVyO1xyXG4gIGNsaWVudDogYW55O1xyXG4gIGNhcnJpZXI/OiBhbnk7XHJcbiAgd29ya190eXBlOiBhbnk7XHJcbiAgY2F0ZWdvcnk6IGFueTtcclxuICBwbGFubmluZ19udW1tYmVycz86IHN0cmluZztcclxuICBpZDogYW55O1xyXG4gIHdvcmtfc3RhdHVzOiBhbnk7XHJcbiAgaGFuZGxlUmVzdW1lVGFzazogYW55O1xyXG4gIGNhcnJpZXJfaWQ6IGFueTtcclxuICB0YXNrX3R5cGU6IGFueTtcclxuICBwYXVzZTogYW55O1xyXG4gIHVzZXI6IGFueTtcclxufVxyXG5cclxuY29uc3QgQ29sdW1uID0gKFxyXG4gIGlzVGltZXJSdW5uaW5nOiBib29sZWFuLFxyXG4gIHNldEVsYXBzZWRUaW1lOiBSZWFjdC5EaXNwYXRjaDxSZWFjdC5TZXRTdGF0ZUFjdGlvbjxudW1iZXI+PixcclxuICBzZXRJc1RpbWVyUnVubmluZzogUmVhY3QuRGlzcGF0Y2g8UmVhY3QuU2V0U3RhdGVBY3Rpb248Ym9vbGVhbj4+LFxyXG4gIHNldFByZXZpb3VzU2VsZWN0ZWRDbGllbnQ6IFJlYWN0LkRpc3BhdGNoPFJlYWN0LlNldFN0YXRlQWN0aW9uPGFueT4+LFxyXG4gIHNldFByZXZpb3VzU2VsZWN0ZWRDYXJyaWVyOiBSZWFjdC5EaXNwYXRjaDxSZWFjdC5TZXRTdGF0ZUFjdGlvbjxhbnk+PixcclxuICBwZXJtaXNzaW9uczogYW55W11cclxuKSA9PiB7XHJcbiAgY29uc3QgY29sdW1ucyA9IFtcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwiZGF0ZVwiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIkRhdGVcIixcclxuICAgICAgd2lkdGg6IDEwMCwgLy8gU2V0IGZpeGVkIHdpZHRoIHRvIGZpdCBmdWxsIGRhdGVcclxuICAgICAgbWluV2lkdGg6IDEwMCwgLy8gUHJldmVudCBzaHJpbmtpbmdcclxuICAgICAgdmFsdWVGb3JtYXR0ZXI6ICh7IHZhbHVlIH0pID0+IHtcclxuICAgICAgICBpZiAoIXZhbHVlKSByZXR1cm4gXCItXCI7XHJcbiAgICAgICAgcmV0dXJuIERhdGVUaW1lLmZyb21JU08odmFsdWUsIHsgem9uZTogXCJ1dGNcIiB9KS50b0Zvcm1hdChcImRkLU1NLXl5eXlcIik7XHJcbiAgICAgIH0sXHJcbiAgICAgIGZpbHRlcjogXCJhZ0RhdGVDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgY29tcGFyYXRvcjogKGZpbHRlckxvY2FsRGF0ZUF0TWlkbmlnaHQ6IERhdGUsIGNlbGxWYWx1ZTogc3RyaW5nKSA9PiB7XHJcbiAgICAgICAgICBpZiAoIWNlbGxWYWx1ZSkgcmV0dXJuIC0xO1xyXG5cclxuICAgICAgICAgIGNvbnN0IGNlbGxEYXRlID0gRGF0ZVRpbWUuZnJvbUlTTyhjZWxsVmFsdWUsIHsgem9uZTogXCJ1dGNcIiB9KVxyXG4gICAgICAgICAgICAuc3RhcnRPZihcImRheVwiKVxyXG4gICAgICAgICAgICAudG9KU0RhdGUoKTtcclxuICAgICAgICAgIGNvbnN0IGZpbHRlckRhdGUgPSBuZXcgRGF0ZShcclxuICAgICAgICAgICAgRGF0ZS5VVEMoXHJcbiAgICAgICAgICAgICAgZmlsdGVyTG9jYWxEYXRlQXRNaWRuaWdodC5nZXRGdWxsWWVhcigpLFxyXG4gICAgICAgICAgICAgIGZpbHRlckxvY2FsRGF0ZUF0TWlkbmlnaHQuZ2V0TW9udGgoKSxcclxuICAgICAgICAgICAgICBmaWx0ZXJMb2NhbERhdGVBdE1pZG5pZ2h0LmdldERhdGUoKVxyXG4gICAgICAgICAgICApXHJcbiAgICAgICAgICApO1xyXG5cclxuICAgICAgICAgIGlmIChjZWxsRGF0ZSA8IGZpbHRlckRhdGUpIHJldHVybiAtMTtcclxuICAgICAgICAgIGlmIChjZWxsRGF0ZSA+IGZpbHRlckRhdGUpIHJldHVybiAxO1xyXG4gICAgICAgICAgcmV0dXJuIDA7XHJcbiAgICAgICAgfSxcclxuICAgICAgICBidXR0b25zOiBbXCJjbGVhclwiXSxcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgIGZpZWxkOiBcInVzZXJuYW1lXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiVXNlcm5hbWVcIixcclxuICAgICAgdmFsdWVHZXR0ZXI6ICh7IGRhdGEgfSkgPT4gZGF0YT8udXNlcj8udXNlcm5hbWUgfHwgXCItXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJjbGllbnRuYW1lXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiQ2xpZW50XCIsXHJcbiAgICAgIHZhbHVlR2V0dGVyOiAoeyBkYXRhIH0pID0+IGRhdGE/LmNsaWVudD8uY2xpZW50X25hbWUgfHwgXCItXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJjYXJyaWVybmFtZVwiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIkNhcnJpZXJcIixcclxuICAgICAgdmFsdWVHZXR0ZXI6ICh7IGRhdGEgfSkgPT5cclxuICAgICAgICBkYXRhPy5jYXJyaWVyX2lkID09PSBudWxsID8gXCJOL0FcIiA6IGRhdGE/LmNhcnJpZXI/Lm5hbWUgfHwgXCItXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJ3b3JrX3R5cGVcIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJXb3JrIFR5cGVcIixcclxuICAgICAgdmFsdWVHZXR0ZXI6ICh7IGRhdGEgfSkgPT4gZGF0YT8ud29ya190eXBlPy53b3JrX3R5cGUgfHwgXCItXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJjYXRlZ29yeVwiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIkNhdGVnb3J5XCIsXHJcbiAgICAgIHZhbHVlR2V0dGVyOiAoeyBkYXRhIH0pID0+IGRhdGE/LmNhdGVnb3J5Py5jYXRlZ29yeV9uYW1lIHx8IFwiLVwiLFxyXG4gICAgICBmaWx0ZXI6IFwiYWdUZXh0Q29sdW1uRmlsdGVyXCIsXHJcbiAgICAgIGZpbHRlclBhcmFtczoge1xyXG4gICAgICAgIGJ1dHRvbnM6IFtcImNsZWFyXCJdLFxyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwidGFza190eXBlXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiVHlwZVwiLFxyXG4gICAgICB2YWx1ZUdldHRlcjogKHsgZGF0YSB9KSA9PiBkYXRhPy50YXNrX3R5cGUgfHwgXCItXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJzdGFydF90aW1lXCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiU3RhcnQgVGltZVwiLFxyXG4gICAgICAvLyB2YWx1ZUZvcm1hdHRlcjogKHsgdmFsdWUgfSkgPT4ge1xyXG4gICAgICAvLyAgIGlmICghdmFsdWUpIHJldHVybiBcIi1cIjtcclxuICAgICAgLy8gICBjb25zdCBkYXRlID0gbmV3IERhdGUodmFsdWUpO1xyXG4gICAgICAvLyAgIHJldHVybiBkYXRlLnRvTG9jYWxlVGltZVN0cmluZyhcImVuLVVTXCIsIHtcclxuICAgICAgLy8gICAgIGhvdXI6IFwiMi1kaWdpdFwiLFxyXG4gICAgICAvLyAgICAgbWludXRlOiBcIjItZGlnaXRcIixcclxuICAgICAgLy8gICAgIGhvdXIxMjogdHJ1ZSxcclxuICAgICAgLy8gICB9KTtcclxuICAgICAgLy8gfSxcclxuICAgICAgdmFsdWVHZXR0ZXI6IChwYXJhbXMpID0+IGZvcm1hdFRpbWVab25lKHBhcmFtcy5kYXRhPy5zdGFydF90aW1lIHx8IFwiXCIpLFxyXG4gICAgICBjZWxsU3R5bGU6IHtcclxuICAgICAgICBkaXNwbGF5OiAnZmxleCcsXHJcbiAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxyXG4gICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxyXG4gICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicsIC8vIEZhbGxiYWNrXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJmaW5pc2hfdGltZVwiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIkZpbmlzaCBUaW1lXCIsXHJcbiAgICAgIC8vIHZhbHVlRm9ybWF0dGVyOiAoeyB2YWx1ZSB9KSA9PiB7XHJcbiAgICAgIC8vICAgaWYgKCF2YWx1ZSkgcmV0dXJuIFwiLVwiO1xyXG4gICAgICAvLyAgIGNvbnN0IGRhdGUgPSBuZXcgRGF0ZSh2YWx1ZSk7XHJcbiAgICAgIC8vICAgcmV0dXJuIGRhdGUudG9Mb2NhbGVUaW1lU3RyaW5nKFwiZW4tVVNcIiwge1xyXG4gICAgICAvLyAgICAgaG91cjogXCIyLWRpZ2l0XCIsXHJcbiAgICAgIC8vICAgICBtaW51dGU6IFwiMi1kaWdpdFwiLFxyXG4gICAgICAvLyAgICAgaG91cjEyOiB0cnVlLFxyXG4gICAgICAvLyAgIH0pO1xyXG4gICAgICAvLyB9LFxyXG4gICAgICB2YWx1ZUdldHRlcjogKHBhcmFtcykgPT4gZm9ybWF0VGltZVpvbmUocGFyYW1zLmRhdGE/LmZpbmlzaF90aW1lIHx8IFwiXCIpLFxyXG4gICAgICBjZWxsU3R5bGU6IHtcclxuICAgICAgICBkaXNwbGF5OiAnZmxleCcsXHJcbiAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxyXG4gICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxyXG4gICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicsIC8vIEZhbGxiYWNrXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAgLy8ge1xyXG4gICAgLy8gICBhY2Nlc3NvcktleTogXCJwYXVzZVwiLFxyXG4gICAgLy8gICBoZWFkZXI6IFwiSGlzdG9yeVwiLFxyXG4gICAgLy8gICBjZWxsOiAoeyByb3cgfSkgPT4ge1xyXG4gICAgLy8gICAgIHJldHVybiAoXHJcbiAgICAvLyAgICAgICA8PlxyXG4gICAgLy8gICAgICAgICA8UGF1c2VSZXN1bWVIaXN0b3J5IGRhdGE9e3Jvdy5vcmlnaW5hbH0gLz5cclxuICAgIC8vICAgICAgIDwvPlxyXG4gICAgLy8gICAgICk7XHJcbiAgICAvLyAgIH0sXHJcbiAgICAvLyB9LFxyXG4gICAgLy8ge1xyXG4gICAgLy8gICBhY2Nlc3NvcktleTogXCJ3b3JrX3N0YXR1c1wiLFxyXG4gICAgLy8gICBoZWFkZXI6IFwiV29ya3N0YXR1c1wiLFxyXG4gICAgLy8gICBjZWxsOiAoeyByb3cgfSkgPT4ge1xyXG4gICAgLy8gICAgIGNvbnN0IGhhbmRsZVJlc3VtZVRhc2sgPSBhc3luYyAoXHJcbiAgICAvLyAgICAgICB3b3JrUmVwb3J0SWQ6IG51bWJlcixcclxuXHJcbiAgICAvLyAgICAgICBpc1RpbWVyUnVubmluZzogYm9vbGVhblxyXG4gICAgLy8gICAgICkgPT4ge1xyXG4gICAgLy8gICAgICAgaWYgKCFpc1RpbWVyUnVubmluZykge1xyXG4gICAgLy8gICAgICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbShcInRpbWVyRGF0YVwiKTtcclxuXHJcbiAgICAvLyAgICAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZm9ybVN1Ym1pdChcclxuICAgIC8vICAgICAgICAgICBgJHt3b3JrcmVwb3J0X3JvdXRlcy5VUERBVEVfV09SS1JFUE9SVH0vJHt3b3JrUmVwb3J0SWR9YCxcclxuICAgIC8vICAgICAgICAgICBcIlBVVFwiLFxyXG5cclxuICAgIC8vICAgICAgICAgICB7XHJcbiAgICAvLyAgICAgICAgICAgICBhY3Rpb246IFwicmVzdW1lXCIsXHJcbiAgICAvLyAgICAgICAgICAgICB3b3JrX3N0YXR1czogXCJSRVNVTUVEXCIsXHJcbiAgICAvLyAgICAgICAgICAgfSxcclxuICAgIC8vICAgICAgICAgICBcIi91c2VyL3RyYWNrZXJcIlxyXG4gICAgLy8gICAgICAgICApO1xyXG5cclxuICAgIC8vICAgICAgICAgaWYgKHJlc3BvbnNlLnN1Y2Nlc3MpIHtcclxuICAgIC8vICAgICAgICAgICBjb25zdCB0aW1lID1cclxuICAgIC8vICAgICAgICAgICAgIHJvdy5vcmlnaW5hbC50aW1lX3NwZW50ICYmXHJcbiAgICAvLyAgICAgICAgICAgICBzdWJ0cmFjdFRpbWUocm93Lm9yaWdpbmFsLnRpbWVfc3BlbnQpO1xyXG5cclxuICAgIC8vICAgICAgICAgICBzZXRJc1RpbWVyUnVubmluZyh0cnVlKTtcclxuICAgIC8vICAgICAgICAgICBzZXRFbGFwc2VkVGltZSgocHJldikgPT4ge1xyXG4gICAgLy8gICAgICAgICAgICAgY29uc3QgdXBkYXRlZFRpbWUgPSBwcmV2ICsgMTtcclxuICAgIC8vICAgICAgICAgICAgIHN0b3JlRGF0YShcInRpbWVyRGF0YVwiLCB7XHJcbiAgICAvLyAgICAgICAgICAgICAgIHN0YXJ0VGltZTogdGltZSxcclxuICAgIC8vICAgICAgICAgICAgICAgZWxhcHNlZFRpbWU6IHVwZGF0ZWRUaW1lLFxyXG4gICAgLy8gICAgICAgICAgICAgfSk7XHJcblxyXG4gICAgLy8gICAgICAgICAgICAgcmV0dXJuIHVwZGF0ZWRUaW1lO1xyXG4gICAgLy8gICAgICAgICAgIH0pO1xyXG5cclxuICAgIC8vICAgICAgICAgICBzZXRQcmV2aW91c1NlbGVjdGVkQ2xpZW50KHJvdy5vcmlnaW5hbC5jbGllbnQpO1xyXG4gICAgLy8gICAgICAgICAgIHNldFByZXZpb3VzU2VsZWN0ZWRDYXJyaWVyKHJvdy5vcmlnaW5hbC5jYXJyaWVyKTtcclxuXHJcbiAgICAvLyAgICAgICAgICAgbG9jYWxTdG9yYWdlLnNldEl0ZW0oXHJcbiAgICAvLyAgICAgICAgICAgICBcIndvcmtUeXBlXCIsXHJcbiAgICAvLyAgICAgICAgICAgICBKU09OLnN0cmluZ2lmeShwYXJzZUludChyb3cub3JpZ2luYWwud29ya190eXBlLmlkKSlcclxuICAgIC8vICAgICAgICAgICApO1xyXG4gICAgLy8gICAgICAgICAgIGxvY2FsU3RvcmFnZS5zZXRJdGVtKFxyXG4gICAgLy8gICAgICAgICAgICAgXCJjbGllbnRcIixcclxuICAgIC8vICAgICAgICAgICAgIEpTT04uc3RyaW5naWZ5KHJvdy5vcmlnaW5hbC5jbGllbnQpXHJcbiAgICAvLyAgICAgICAgICAgKTtcclxuICAgIC8vICAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbShcclxuICAgIC8vICAgICAgICAgICAgIFwiY2FycmllclwiLFxyXG4gICAgLy8gICAgICAgICAgICAgSlNPTi5zdHJpbmdpZnkocm93Lm9yaWdpbmFsLmNhcnJpZXIpXHJcbiAgICAvLyAgICAgICAgICAgKTtcclxuXHJcbiAgICAvLyAgICAgICAgICAgcm91dGVyLnJlZnJlc2goKTtcclxuICAgIC8vICAgICAgICAgfVxyXG4gICAgLy8gICAgICAgfSBlbHNlIHtcclxuICAgIC8vICAgICAgICAgdG9hc3QuZXJyb3IoXCJUaW1lciBpcyBydW5uaW5nLiBQYXVzZSBvciBzdG9wIGl0IGZpcnN0LlwiKTtcclxuICAgIC8vICAgICAgIH1cclxuICAgIC8vICAgICB9O1xyXG4gICAgLy8gICAgIGNvbnN0IHdvcmtfc3RhdHVzID0gcm93Lm9yaWdpbmFsLndvcmtfc3RhdHVzO1xyXG4gICAgLy8gICAgIGlmICh3b3JrX3N0YXR1cyA9PT0gXCJQQVVTRURcIikge1xyXG4gICAgLy8gICAgICAgcmV0dXJuIChcclxuICAgIC8vICAgICAgICAgPEJhZGdlXHJcbiAgICAvLyAgICAgICAgICAgLy8gb25DbGljaz17KCk9PnsgKCdvbmNsaWNrJyl9fVxyXG4gICAgLy8gICAgICAgICAgIG9uQ2xpY2s9eygpID0+IGhhbmRsZVJlc3VtZVRhc2socm93Lm9yaWdpbmFsLmlkLCBpc1RpbWVyUnVubmluZyl9XHJcbiAgICAvLyAgICAgICAgICAgY2xhc3NOYW1lPVwiIGN1cnNvci1wb2ludGVyIHRleHQtY2VudGVyIHctMjAgIGZsZXggaXRlbXMtY2VudGVyIHRleHQtd2hpdGUgYmctb3JhbmdlLTUwMCBob3ZlcjpiZy1vcmFuZ2UtNjAwIFwiXHJcbiAgICAvLyAgICAgICAgID5cclxuICAgIC8vICAgICAgICAgICB7fVxyXG4gICAgLy8gICAgICAgICAgIFBBVVNFRFxyXG4gICAgLy8gICAgICAgICAgIHsvKiA8RmFQbGF5IGNsYXNzTmFtZT1cInRleHQtc20gXCIgLz4gKi99XHJcbiAgICAvLyAgICAgICAgIDwvQmFkZ2U+XHJcbiAgICAvLyAgICAgICApO1xyXG4gICAgLy8gICAgIH1cclxuICAgIC8vICAgICByZXR1cm4gKFxyXG4gICAgLy8gICAgICAgPEJhZGdlXHJcbiAgICAvLyAgICAgICAgIGNsYXNzTmFtZT17YGN1cnNvci1wb2ludGVyIGZsZXggaXRlbXMtY2VudGVyIGdhcC0yIHRleHQtY2VudGVyIHctMjAgIGp1c3RpZnktY2VudGVyIHRleHQtd2hpdGUgJHtcclxuICAgIC8vICAgICAgICAgICB3b3JrX3N0YXR1cyA9PT0gXCJGSU5JU0hFRFwiXHJcbiAgICAvLyAgICAgICAgICAgICA/IFwiYmctZ3JheS01MDBcIlxyXG4gICAgLy8gICAgICAgICAgICAgOiB3b3JrX3N0YXR1cyA9PT0gXCJSRVNVTUVEXCJcclxuICAgIC8vICAgICAgICAgICAgID8gXCJiZy1ibHVlLTUwMFwiXHJcbiAgICAvLyAgICAgICAgICAgICA6IFwiYmctZ3JlZW4tNTAwXCJcclxuICAgIC8vICAgICAgICAgfSBjdXJzb3ItcG9pbnRlcmB9XHJcbiAgICAvLyAgICAgICA+XHJcbiAgICAvLyAgICAgICAgIHt3b3JrX3N0YXR1c31cclxuICAgIC8vICAgICAgIDwvQmFkZ2U+XHJcbiAgICAvLyAgICAgKTtcclxuICAgIC8vICAgfSxcclxuICAgIC8vIH0sXHJcblxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJ0aW1lX3NwZW50XCIsXHJcbiAgICAgIGhlYWRlck5hbWU6IFwiVGltZSBTcGVudFwiLFxyXG4gICAgICB2YWx1ZUdldHRlcjogKHsgZGF0YSB9KSA9PiB7XHJcbiAgICAgICAgY29uc3QgdGltZVNwZW50ID0gZGF0YT8udGltZV9zcGVudDtcclxuICAgICAgICBpZiAoIXRpbWVTcGVudCkgcmV0dXJuIFwiLVwiO1xyXG4gICAgICAgIGNvbnN0IGZvcm1hdHRlZCA9IGZvcm1hdER1cmF0aW9uKHRpbWVTcGVudCk7XHJcbiAgICAgICAgY29uc3QgW2hvdXJzLCBtaW51dGVzXSA9IGZvcm1hdHRlZC5zcGxpdChcIjpcIik7XHJcbiAgICAgICAgcmV0dXJuIGAke2hvdXJzfToke21pbnV0ZXN9YDtcclxuICAgICAgfSxcclxuICAgICAgY2VsbFN0eWxlOiB7XHJcbiAgICAgICAgZGlzcGxheTogJ2ZsZXgnLFxyXG4gICAgICAgIGp1c3RpZnlDb250ZW50OiAnY2VudGVyJyxcclxuICAgICAgICBhbGlnbkl0ZW1zOiAnY2VudGVyJyxcclxuICAgICAgICB0ZXh0QWxpZ246ICdjZW50ZXInLCAvLyBGYWxsYmFja1xyXG4gICAgICB9LFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgZmllbGQ6IFwiYWN0dWFsX251bWJlclwiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIkFjdHVhbCBOb1wiLFxyXG4gICAgICBmaWx0ZXI6IFwiYWdUZXh0Q29sdW1uRmlsdGVyXCIsXHJcbiAgICAgIGZpbHRlclBhcmFtczoge1xyXG4gICAgICAgIGJ1dHRvbnM6IFtcImNsZWFyXCJdLFxyXG4gICAgICB9LFxyXG4gICAgICBjZWxsU3R5bGU6IHtcclxuICAgICAgICBkaXNwbGF5OiAnZmxleCcsXHJcbiAgICAgICAganVzdGlmeUNvbnRlbnQ6ICdjZW50ZXInLFxyXG4gICAgICAgIGFsaWduSXRlbXM6ICdjZW50ZXInLFxyXG4gICAgICAgIHRleHRBbGlnbjogJ2NlbnRlcicsIC8vIEZhbGxiYWNrXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJub3Rlc1wiLFxyXG4gICAgICBoZWFkZXJOYW1lOiBcIk5vdGVzXCIsXHJcbiAgICAgIGZpbHRlcjogXCJhZ1RleHRDb2x1bW5GaWx0ZXJcIixcclxuICAgICAgZmlsdGVyUGFyYW1zOiB7XHJcbiAgICAgICAgYnV0dG9uczogW1wiY2xlYXJcIl0sXHJcbiAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICBmaWVsZDogXCJhY3Rpb25cIixcclxuICAgICAgaGVhZGVyTmFtZTogXCJBY3Rpb25cIixcclxuICAgICAgY2VsbFJlbmRlcmVyOiAocGFyYW1zOiBhbnkpID0+IHtcclxuICAgICAgICBjb25zdCB3b3JrUmVwb3J0ID0gcGFyYW1zPy5kYXRhO1xyXG4gICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGdhcC0yXCI+XHJcbiAgICAgICAgICAgIDxQZXJtaXNzaW9uV3JhcHBlclxyXG4gICAgICAgICAgICAgIHBlcm1pc3Npb25zPXtwZXJtaXNzaW9uc31cclxuICAgICAgICAgICAgICByZXF1aXJlZFBlcm1pc3Npb25zPXtbXCJ1cGRhdGUtdHJhY2tlclwiXX1cclxuICAgICAgICAgICAgPlxyXG4gICAgICAgICAgICAgIDxVcGRhdGVUcmFja2VyIHdvcmtSZXBvcnQ9e3dvcmtSZXBvcnR9IC8+XHJcbiAgICAgICAgICAgIDwvUGVybWlzc2lvbldyYXBwZXI+XHJcbiAgICAgICAgICA8L2Rpdj5cclxuICAgICAgICApO1xyXG4gICAgICB9LFxyXG4gICAgICBzb3J0YWJsZTogZmFsc2UsXHJcbiAgICAgIGNlbGxTdHlsZToge1xyXG4gICAgICAgIGRpc3BsYXk6ICdmbGV4JyxcclxuICAgICAgICBqdXN0aWZ5Q29udGVudDogJ2NlbnRlcicsXHJcbiAgICAgICAgYWxpZ25JdGVtczogJ2NlbnRlcicsXHJcbiAgICAgICAgdGV4dEFsaWduOiAnY2VudGVyJywgLy8gRmFsbGJhY2tcclxuICAgICAgfSxcclxuICAgIH0sXHJcbiAgXTtcclxuICByZXR1cm4gY29sdW1ucztcclxufTtcclxuXHJcbmV4cG9ydCBkZWZhdWx0IENvbHVtbjtcclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiZm9ybWF0RHVyYXRpb24iLCJmb3JtYXRUaW1lWm9uZSIsIlVwZGF0ZVRyYWNrZXIiLCJQZXJtaXNzaW9uV3JhcHBlciIsIkRhdGVUaW1lIiwiQ29sdW1uIiwiaXNUaW1lclJ1bm5pbmciLCJzZXRFbGFwc2VkVGltZSIsInNldElzVGltZXJSdW5uaW5nIiwic2V0UHJldmlvdXNTZWxlY3RlZENsaWVudCIsInNldFByZXZpb3VzU2VsZWN0ZWRDYXJyaWVyIiwicGVybWlzc2lvbnMiLCJjb2x1bW5zIiwiZmllbGQiLCJoZWFkZXJOYW1lIiwid2lkdGgiLCJtaW5XaWR0aCIsInZhbHVlRm9ybWF0dGVyIiwidmFsdWUiLCJmcm9tSVNPIiwiem9uZSIsInRvRm9ybWF0IiwiZmlsdGVyIiwiZmlsdGVyUGFyYW1zIiwiY29tcGFyYXRvciIsImZpbHRlckxvY2FsRGF0ZUF0TWlkbmlnaHQiLCJjZWxsVmFsdWUiLCJjZWxsRGF0ZSIsInN0YXJ0T2YiLCJ0b0pTRGF0ZSIsImZpbHRlckRhdGUiLCJEYXRlIiwiVVRDIiwiZ2V0RnVsbFllYXIiLCJnZXRNb250aCIsImdldERhdGUiLCJidXR0b25zIiwidmFsdWVHZXR0ZXIiLCJkYXRhIiwidXNlciIsInVzZXJuYW1lIiwiY2xpZW50IiwiY2xpZW50X25hbWUiLCJjYXJyaWVyX2lkIiwiY2FycmllciIsIm5hbWUiLCJ3b3JrX3R5cGUiLCJjYXRlZ29yeSIsImNhdGVnb3J5X25hbWUiLCJ0YXNrX3R5cGUiLCJwYXJhbXMiLCJzdGFydF90aW1lIiwiY2VsbFN0eWxlIiwiZGlzcGxheSIsImp1c3RpZnlDb250ZW50IiwiYWxpZ25JdGVtcyIsInRleHRBbGlnbiIsImZpbmlzaF90aW1lIiwidGltZVNwZW50IiwidGltZV9zcGVudCIsImZvcm1hdHRlZCIsImhvdXJzIiwibWludXRlcyIsInNwbGl0IiwiY2VsbFJlbmRlcmVyIiwid29ya1JlcG9ydCIsImRpdiIsImNsYXNzTmFtZSIsInJlcXVpcmVkUGVybWlzc2lvbnMiLCJzb3J0YWJsZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/user/tracker/column.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/ui/customInput.tsx":
/*!***************************************!*\
  !*** ./components/ui/customInput.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CustomInput: function() { return /* binding */ CustomInput; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n\n\n\nconst CustomInput = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(_c = (param, ref)=>{\n    let { className, type, ...props } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-8 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"D:\\\\PMS\\\\pms-moon\\\\client\\\\components\\\\ui\\\\customInput.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\n_c1 = CustomInput;\nCustomInput.displayName = \"Input\";\n\nvar _c, _c1;\n$RefreshReg$(_c, \"CustomInput$React.forwardRef\");\n$RefreshReg$(_c1, \"CustomInput\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvdWkvY3VzdG9tSW5wdXQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUE4QjtBQUVFO0FBRWhDLE1BQU1FLDRCQUFjRiw2Q0FBZ0IsTUFDbEMsUUFBZ0NJO1FBQS9CLEVBQUVDLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU87SUFDNUIscUJBQ0UsOERBQUNDO1FBQ0NGLE1BQU1BO1FBQ05ELFdBQVdKLDhDQUFFQSxDQUNYLHNWQUNBSTtRQUVGRCxLQUFLQTtRQUNKLEdBQUdHLEtBQUs7Ozs7OztBQUdmOztBQUVGTCxZQUFZTyxXQUFXLEdBQUc7QUFFSiIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb21wb25lbnRzL3VpL2N1c3RvbUlucHV0LnRzeD81Nzk1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCAqIGFzIFJlYWN0IGZyb20gXCJyZWFjdFwiXHJcblxyXG5pbXBvcnQgeyBjbiB9IGZyb20gXCJAL2xpYi91dGlsc1wiXHJcblxyXG5jb25zdCBDdXN0b21JbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJpbnB1dFwiPj4oXHJcbiAgKHsgY2xhc3NOYW1lLCB0eXBlLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcclxuICAgIHJldHVybiAoXHJcbiAgICAgIDxpbnB1dFxyXG4gICAgICAgIHR5cGU9e3R5cGV9XHJcbiAgICAgICAgY2xhc3NOYW1lPXtjbihcclxuICAgICAgICAgIFwiZmxleCBoLTggdy1mdWxsIHJvdW5kZWQtbWQgYm9yZGVyIGJvcmRlci1pbnB1dCBiZy10cmFuc3BhcmVudCBweC0zIHB5LTEgdGV4dC1iYXNlIHNoYWRvdy1zbSB0cmFuc2l0aW9uLWNvbG9ycyBmaWxlOmJvcmRlci0wIGZpbGU6YmctdHJhbnNwYXJlbnQgZmlsZTp0ZXh0LXNtIGZpbGU6Zm9udC1tZWRpdW0gZmlsZTp0ZXh0LWZvcmVncm91bmQgcGxhY2Vob2xkZXI6dGV4dC1tdXRlZC1mb3JlZ3JvdW5kIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBkaXNhYmxlZDpvcGFjaXR5LTUwIG1kOnRleHQtc21cIixcclxuICAgICAgICAgIGNsYXNzTmFtZVxyXG4gICAgICAgICl9XHJcbiAgICAgICAgcmVmPXtyZWZ9XHJcbiAgICAgICAgey4uLnByb3BzfVxyXG4gICAgICAvPlxyXG4gICAgKVxyXG4gIH1cclxuKVxyXG5DdXN0b21JbnB1dC5kaXNwbGF5TmFtZSA9IFwiSW5wdXRcIlxyXG5cclxuZXhwb3J0IHsgQ3VzdG9tSW5wdXQgfVxyXG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjbiIsIkN1c3RvbUlucHV0IiwiZm9yd2FyZFJlZiIsInJlZiIsImNsYXNzTmFtZSIsInR5cGUiLCJwcm9wcyIsImlucHV0IiwiZGlzcGxheU5hbWUiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/ui/customInput.tsx\n"));

/***/ })

});