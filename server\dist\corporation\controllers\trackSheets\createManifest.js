"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.createOrUpdateManifestDetails = void 0;
const createOrUpdateManifestDetails = async (req, res) => {
    try {
        const { trackSheetId, manifestStatus, manifestDate, manifestNotes, actionRequired, createdBy, updatedBy, } = req.body;
        if (!trackSheetId) {
            return res.status(400).json({ error: "trackSheetId is required." });
        }
        const result = await prisma.manifestDetailsSchema.upsert({
            where: { trackSheetId: Number(trackSheetId) },
            create: {
                trackSheetId: Number(trackSheetId),
                manifestStatus,
                manifestDate: manifestDate ? new Date(manifestDate) : undefined,
                manifestNotes,
                actionRequired,
                createdBy,
            },
            update: {
                manifestStatus,
                manifestDate: manifestDate ? new Date(manifestDate) : undefined,
                manifestNotes,
                actionRequired,
                updatedBy,
            },
        });
        return res
            .status(200)
            .json({ success: true, message: "Manifest details saved", data: result });
    }
    catch (err) {
        console.error(err);
        return res
            .status(500)
            .json({ error: "Failed to create or update manifest details" });
    }
};
exports.createOrUpdateManifestDetails = createOrUpdateManifestDetails;
//# sourceMappingURL=createManifest.js.map