"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createClientFTPFilePathConfig = void 0;
const prismaClient_1 = __importDefault(require("../../../utils/prismaClient"));
const createClientFTPFilePathConfig = async (req, res) => {
    try {
        const { clientId, filePath } = req.body;
        if (!clientId || !filePath) {
            return res
                .status(400)
                .json({ error: "clientId and filePath are required" });
        }
        const created = await prismaClient_1.default.clientFTPFilePathConfig.create({
            data: {
                clientId: Number(clientId),
                filePath: String(filePath),
            },
        });
        return res.json({
            success: true,
            message: "File path created!",
        });
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
};
exports.createClientFTPFilePathConfig = createClientFTPFilePathConfig;
exports.default = exports.createClientFTPFilePathConfig;
//# sourceMappingURL=create.js.map