import { handleError } from "../../../../utils/helpers";

/**
 * Get tickets analytics overview
 * Provides high-level metrics and KPIs for tickets
 * 
 * @param req - Express request object with query parameters for filtering
 * @param res - Express response object
 * @returns Overview metrics including total tickets, closure rates, and average times
 */
export const getTicketsAnalyticsOverview = async (req: any, res: any) => {
  try {
    const {
      dateFrom,
      dateTo,
      tags,
      assignedTo,
      stageId,
      priority,
    } = req.query;

    // Build where clause for filtering
    const whereClause: any = {
      deletedAt: null,
    };

    // Add date filtering
    if (dateFrom || dateTo) {
      whereClause.createdAt = {};
      if (dateFrom) {
        whereClause.createdAt.gte = new Date(dateFrom);
      }
      if (dateTo) {
        whereClause.createdAt.lte = new Date(dateTo);
      }
    }

    // Add tag filtering
    if (tags) {
      const tagArray = tags.split(',').map((tag: string) => tag.trim());
      whereClause.tags = {
        hasSome: tagArray,
      };
    }

    // Add priority filtering
    if (priority) {
      whereClause.priority = priority;
    }
 

    // Add assignedTo filtering - check in ticket stages
    const stageWhereClause: any = {};
    if (assignedTo) {
      stageWhereClause.assignedTo = assignedTo;
    }
    if (stageId) {
      stageWhereClause.pipelineStageId = stageId;
    }

    // Get total tickets count
    const totalTickets = await prisma.ticket.count({
      where: whereClause,
    });

    // Get tickets with stages to determine closure status
    const ticketsWithStages = await prisma.ticket.findMany({
      where: {
        ...whereClause,
        ...(Object.keys(stageWhereClause).length > 0 && {
          stages: {
            some: stageWhereClause,
          },
        }),
      },
      include: {
        pipeline: {
          include: {
            stages: {
              orderBy: { order: 'asc' }, // Ascending to get first to last
            },
          },
        },
        stages: {
          include: {
            pipelineStage: true,
          },
          orderBy: {
            createdAt: 'asc', // Ascending to match pipeline order
          },
        },
      },
    });

    // Calculate closed tickets (tickets that reached the final stage of their pipeline)
    let closedTickets = 0;
    let totalResolutionTime = 0;
    let resolvedTicketsCount = 0;

    for (const ticket of ticketsWithStages) {
      if (
        ticket.pipeline?.stages?.length > 0 &&
        ticket.stages?.length > 0 &&
        ticket.currentStageId
      ) {
        const finalStage = ticket.pipeline.stages[ticket.pipeline.stages.length - 1]; // Last stage in pipeline
        // Find the current stage by currentStageId
        const currentStage = ticket.stages.find(
          (stage) => stage.pipelineStageId === ticket.currentStageId
        );
        if (currentStage && ticket.currentStageId === finalStage.id) {
          closedTickets++;
          // Calculate resolution time
          const createdAt = new Date(ticket.createdAt);
          const closedAt = new Date(currentStage.createdAt);
          const resolutionTimeHours =
            (closedAt.getTime() - createdAt.getTime()) / (1000 * 60 * 60);
          totalResolutionTime += resolutionTimeHours;
          resolvedTicketsCount++;
        }
      }
    }

    const openTickets = totalTickets - closedTickets;
    const closureRate = totalTickets > 0 ? (closedTickets / totalTickets) * 100 : 0;
    const averageTimeToClose =
      resolvedTicketsCount > 0 ? totalResolutionTime / resolvedTicketsCount : 0;

    // Count tickets created in the specified period
    let ticketsCreatedInPeriod = totalTickets;
    if (dateFrom || dateTo) {
      ticketsCreatedInPeriod = await prisma.ticket.count({
        where: {
          ...whereClause,
          createdAt: whereClause.createdAt,
        },
      });
    }

    // Count tickets closed in the specified period
    let ticketsClosedInPeriod = 0;
    if (dateFrom || dateTo) {
      const allClosedTickets = ticketsWithStages.filter((ticket) => {
        if (
          ticket.pipeline?.stages?.length > 0 &&
          ticket.stages?.length > 0 &&
          ticket.currentStageId
        ) {
          const finalStage = ticket.pipeline.stages[ticket.pipeline.stages.length - 1];
          const currentStage = ticket.stages.find(
            (stage) => stage.pipelineStageId === ticket.currentStageId
          );
          if (currentStage && ticket.currentStageId === finalStage.id) {
            const closedAt = new Date(currentStage.createdAt);
            if (dateFrom && dateTo) {
              return closedAt >= new Date(dateFrom) && closedAt <= new Date(dateTo);
            } else if (dateFrom) {
              return closedAt >= new Date(dateFrom);
            } else if (dateTo) {
              return closedAt <= new Date(dateTo);
            }
            return true;
          }
        }
        return false;
      });
      ticketsClosedInPeriod = allClosedTickets.length;
    } else {
      ticketsClosedInPeriod = closedTickets;
    }

    return res.status(200).json({
      success: true,
      data: {
        totalTickets,
        closedTickets,
        openTickets,
        averageTimeToClose: Math.round(averageTimeToClose * 100) / 100,
        averageTimeToCloseUnit: "hours",
        closureRate: Math.round(closureRate * 100) / 100,
        ticketsCreatedInPeriod,
        ticketsClosedInPeriod,
      },
    });
  } catch (error) {
    console.error("Error in getTicketsAnalyticsOverview:", error);
    return handleError(res, error);
  }
};

