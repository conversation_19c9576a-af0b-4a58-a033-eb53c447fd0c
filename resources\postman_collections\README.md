# Ticketing Analytics API - Postman Collection

This directory contains importable Postman collections for testing and documenting APIs in the PMS system.

## Available Collections

### 📊 Ticketing Analytics API Collection
**File:** `ticketing_analytics_api.json`

Comprehensive collection for testing all ticketing analytics and reporting endpoints.

#### Quick Start
1. Import the collection into Postman
2. Set up environment variables (see below)
3. Run individual requests or the entire collection

#### Environment Variables
Set these variables in your Postman environment:

| Variable | Default Value | Description |
|----------|---------------|-------------|
| `baseUrl` | `http://localhost:3000` | API server base URL |
| `corporationId` | `1` | Corporation ID for multi-tenancy |
| `authToken` | `your-jwt-token-here` | JWT authentication token |
| `dateFrom` | Auto-generated | Start date (1 month ago) |
| `dateTo` | Auto-generated | End date (current date) |

#### Available Endpoints

1. **Analytics Overview** (`GET /api/tickets/analytics/overview`)
   - High-level metrics and KPIs
   - Total counts, closure rates, resolution times

2. **Distribution by Stage** (`GET /api/tickets/analytics/distribution-by-stage`)
   - Ticket distribution across pipeline stages
   - Counts and percentages per stage

3. **Closure Rate by Stage** (`GET /api/tickets/analytics/closure-rate-by-stage`)
   - Success/completion rate per stage
   - Time tracking metrics

4. **Average Time by Stage** (`GET /api/tickets/analytics/average-time-by-stage`)
   - Detailed time statistics per stage
   - Average, median, min, max times

5. **Closure Rate Insights** (`GET /api/tickets/analytics/insights/closure-rate`)
   - Performance categorization (HIGH/MEDIUM/LOW)
   - Actionable recommendations

6. **Resolution Time Insights** (`GET /api/tickets/analytics/insights/resolution-time`)
   - Resolution time analysis by priority
   - Performance benchmarks and recommendations

#### Common Query Parameters

All endpoints support these optional filters:

- `dateFrom`: ISO 8601 date string (e.g., "2024-01-01T00:00:00Z")
- `dateTo`: ISO 8601 date string (e.g., "2024-12-31T23:59:59Z")
- `tags`: Comma-separated list of tag IDs
- `assignedTo`: User ID or username
- `stageId`: Pipeline stage ID
- `priority`: Ticket priority (HIGH, MEDIUM, LOW)
- `corporationId`: Corporation ID (required for all requests)

#### Test Scripts

Each endpoint includes automated test scripts that validate:
- ✅ Response status codes
- ✅ Response structure and required fields
- ✅ Data types and formats
- ✅ Performance (response time < 5 seconds)
- ✅ Content-Type headers

#### Example Usage

1. **Get Overview for Last Month:**
   ```
   GET {{baseUrl}}/api/tickets/analytics/overview?corporationId={{corporationId}}
   ```

2. **Filter by High Priority Tickets:**
   ```
   GET {{baseUrl}}/api/tickets/analytics/distribution-by-stage?corporationId=1&priority=HIGH
   ```

3. **Analyze Specific Date Range:**
   ```
   GET {{baseUrl}}/api/tickets/analytics/closure-rate-by-stage?corporationId=1&dateFrom=2024-01-01T00:00:00Z&dateTo=2024-03-31T23:59:59Z
   ```

## Import Instructions

### Postman Desktop/Web
1. Open Postman
2. Click "Import" button
3. Select "File" tab
4. Choose the JSON file from this directory
5. Click "Import"

### Newman (CLI)
```bash
# Install Newman globally
npm install -g newman

# Run the collection
newman run ticketing_analytics_api.json \
  --environment your-environment.json \
  --reporters cli,html \
  --reporter-html-export results.html
```

## Response Format

All endpoints follow a consistent response format:

```json
{
  "success": true,
  "data": {
    // Endpoint-specific data structure
  },
  "error": null  // Present only on errors
}
```

## Error Handling

Common error responses:

- `400 Bad Request`: Invalid query parameters
- `401 Unauthorized`: Missing or invalid authentication
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Internal Server Error`: Server-side errors

## Performance Expectations

- Response time: < 5 seconds (tested automatically)
- Rate limiting: Not implemented (add if needed)
- Caching: Responses may be cached for performance

## Contributing

When adding new endpoints to this collection:

1. Follow the existing naming convention
2. Include comprehensive test scripts
3. Add parameter descriptions
4. Provide example responses
5. Update this README with new endpoints
6. Validate the collection imports correctly

## Support

For issues with the API endpoints or collection:
1. Check server logs for detailed error messages
2. Verify database connectivity and schema
3. Ensure proper environment variable configuration
4. Review authentication setup if using protected endpoints

---
*Generated automatically as part of PMS ticketing analytics implementation*
