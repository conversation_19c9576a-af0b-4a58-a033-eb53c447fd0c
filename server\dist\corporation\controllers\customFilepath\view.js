"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.viewClientFTPFilePathConfig = exports.viewAllClientFTPFilePathConfigs = void 0;
const prismaClient_1 = __importDefault(require("../../../utils/prismaClient"));
const viewAllClientFTPFilePathConfigs = async (req, res) => {
    try {
        const configs = await prismaClient_1.default.clientFTPFilePathConfig.findMany({
            orderBy: { createdAt: "desc" },
        });
        return res.status(200).json({ data: configs });
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
};
exports.viewAllClientFTPFilePathConfigs = viewAllClientFTPFilePathConfigs;
const viewClientFTPFilePathConfig = async (req, res) => {
    const { clientId } = req.query;
    if (!clientId) {
        return res.status(400).json({ error: "clientId query parameter is required" });
    }
    const numericClientId = Number(clientId);
    try {
        const data = await prismaClient_1.default.clientFTPFilePathConfig.findMany({
            where: { clientId: numericClientId },
        });
        return res.json({ success: true, data });
    }
    catch (error) {
        return res.status(500).json({ error: "Internal Server Error" });
    }
};
exports.viewClientFTPFilePathConfig = viewClientFTPFilePathConfig;
exports.default = exports.viewClientFTPFilePathConfig;
//# sourceMappingURL=view.js.map