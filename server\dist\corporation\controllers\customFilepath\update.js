"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateClientFTPFilePathConfig = void 0;
const prismaClient_1 = __importDefault(require("../../../utils/prismaClient"));
const updateClientFTPFilePathConfig = async (req, res) => {
    try {
        const { clientId, filePath, newFilePath } = req.body;
        if (!clientId || !filePath || !newFilePath) {
            return res.status(400).json({ error: "clientId, filePath, and newFilePath are required" });
        }
        // Update using clientId as the primary key
        const updated = await prismaClient_1.default.clientFTPFilePathConfig.update({
            where: {
                clientId: Number(clientId)
            },
            data: {
                filePath: String(newFilePath),
            },
        });
        return res.status(200).json({
            success: true,
            message: "File path updated successfully",
            data: updated
        });
    }
    catch (error) {
        console.error("Error updating file path:", error);
        return res.status(500).json({ error: error.message });
    }
};
exports.updateClientFTPFilePathConfig = updateClientFTPFilePathConfig;
exports.default = exports.updateClientFTPFilePathConfig;
//# sourceMappingURL=update.js.map