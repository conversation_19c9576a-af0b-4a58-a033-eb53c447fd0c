"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateTrackSheets = exports.customFieldsService = void 0;
const operation_1 = require("../../../utils/operation");
const helpers_1 = require("../../../utils/helpers");
const prismaClient_1 = __importDefault(require("../../../utils/prismaClient"));
const customFieldsService = async (trackSheetId, customFieldsData) => {
    try {
        // Get existing mappings
        const existingMappings = await prismaClient_1.default.trackSheetCustomFieldMapping.findMany({
            where: {
                tracksheetId: trackSheetId,
            },
        });
        // Create a map of existing mappings by customFieldId
        const existingMap = new Map(existingMappings.map(m => [m.customFieldId, m]));
        // Process each custom field
        const mappings = [];
        for (const [customFieldId, value] of Object.entries(customFieldsData)) {
            if (value !== "" && typeof value === 'string') {
                const existingMapping = existingMap.get(customFieldId);
                if (existingMapping) {
                    // Update existing mapping
                    const mapping = await prismaClient_1.default.trackSheetCustomFieldMapping.update({
                        where: {
                            id: existingMapping.id
                        },
                        data: {
                            value: value
                        }
                    });
                    mappings.push(mapping);
                }
                else {
                    // Create new mapping only if it doesn't exist
                    const mapping = await prismaClient_1.default.trackSheetCustomFieldMapping.create({
                        data: {
                            tracksheetId: trackSheetId,
                            customFieldId: customFieldId,
                            value: value,
                        },
                    });
                    mappings.push(mapping);
                }
            }
        }
        // Delete mappings that are no longer present
        const currentFieldIds = Object.keys(customFieldsData);
        const mappingsToDelete = existingMappings.filter(m => !currentFieldIds.includes(m.customFieldId));
        if (mappingsToDelete.length > 0) {
            await prismaClient_1.default.trackSheetCustomFieldMapping.deleteMany({
                where: {
                    id: {
                        in: mappingsToDelete.map(m => m.id)
                    }
                }
            });
        }
        return mappings;
    }
    catch (error) {
        throw error;
    }
};
exports.customFieldsService = customFieldsService;
const updateTrackSheets = async (req, res) => {
    const id = req.params.id;
    const { corporationID } = req;
    try {
        // Extract custom fields from the request body
        let customFields = {};
        if (req.body.customFields) {
            try {
                customFields = typeof req.body.customFields === 'string'
                    ? JSON.parse(req.body.customFields)
                    : req.body.customFields;
            }
            catch (e) {
                console.error('Error parsing custom fields:', e);
                customFields = {};
            }
        }
        // Construct fields
        const fields = {
            clientId: req.body.clientId ? parseInt(req.body.clientId) : null,
            company: req.body.company,
            division: req.body.division,
            invoice: req.body.invoice,
            masterInvoice: req.body.masterInvoice,
            bol: req.body.bol,
            invoiceDate: req.body.invoiceDate ? new Date(req.body.invoiceDate) : null,
            receivedDate: req.body.receivedDate
                ? new Date(req.body.receivedDate)
                : null,
            shipmentDate: req.body.shipmentDate
                ? new Date(req.body.shipmentDate)
                : null,
            carrierId: req.body.carrierId ? Number(req.body.carrierId) : null,
            invoiceStatus: req.body.invoiceStatus,
            currency: req.body.currency,
            qtyShipped: req.body.qtyShipped ? Number(req.body.qtyShipped) : null,
            weightUnitName: req.body.weightUnitName,
            quantityBilledText: req.body.quantityBilledText,
            freightClass: req.body.freightClass,
            invoiceTotal: req.body.invoiceTotal
                ? Number(req.body.invoiceTotal)
                : null,
            savings: req.body.savings,
            ftpFileName: req.body.ftpFileName,
            ftpPage: req.body.ftpPage,
            notes: req.body.notes,
            manualMatching: req.body.manualMatching,
            invoiceType: req.body.invoiceType,
            mistake: req.body.mistake,
            docAvailable: req.body.docAvailable || null,
            filePath: req.body.filePath,
        };
        // Update the main track sheet record
        const result = await (0, operation_1.updateItemTrackSheet)({
            model: "TrackSheets",
            fieldName: "id",
            fields,
            id: Number(id),
            res,
            req,
            successMessage: "TrackSheet updated successfully",
        });
        // Handle custom fields if any exist
        if (Object.keys(customFields).length > 0) {
            await (0, exports.customFieldsService)(Number(id), customFields);
        }
        return result;
    }
    catch (err) {
        console.error("Error updating TrackSheet:", err);
        return (0, helpers_1.handleError)(res, err);
    }
};
exports.updateTrackSheets = updateTrackSheets;
//# sourceMappingURL=update.js.map