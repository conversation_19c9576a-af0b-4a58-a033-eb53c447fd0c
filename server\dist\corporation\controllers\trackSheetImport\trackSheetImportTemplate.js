"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.downloadTrackSheetTemplate = void 0;
const json2csv_1 = require("json2csv");
const downloadTrackSheetTemplate = async (req, res) => {
    try {
        const baseFields = [
            "Client",
            "FTP File Name",
            "FTP Page",
            "Carrier",
            "Bill To Client",
            "Company",
            "Division",
            "Manual Matching",
            "Master Invoice",
            "Invoice",
            "Bol",
            "Received Date",
            "Invoice Date",
            "Shipment Date",
            "Invoice Total",
            "Currency",
            "Savings",
            "Notes",
            "Freight Class",
            "Weight Unit",
            "Quantity Billed",
            "Qty Shipped",
            "Invoice Type",
            "Invoice Status",
            "Notes (Remarks)",
            "Doc Available",
        ];
        const fields = [...baseFields];
        const parser = new json2csv_1.Parser({ fields });
        // Only output the headers, no data rows
        const csv = parser.parse([]);
        res.setHeader("Content-Type", "text/csv");
        res.setHeader("Content-Disposition", "attachment; filename=track_sheet_template.csv");
        res.send(csv);
    }
    catch (error) {
        console.error("Error generating track sheet template:", error);
        res.status(500).json({ error: "Failed to generate track sheet template" });
    }
};
exports.downloadTrackSheetTemplate = downloadTrackSheetTemplate;
//# sourceMappingURL=trackSheetImportTemplate.js.map