"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateCustomFieldOrder = exports.getClientCustomFieldsByClientId = exports.createClientCustomField = exports.getCustomFieldsWithClients = exports.getCustomFields = exports.createCustomField = exports.mandatoryFields = void 0;
const client_1 = require("@prisma/client");
const prisma = new client_1.PrismaClient();
// --- Helper function to map field type ---
function mapFieldType(type) {
    switch (type.toUpperCase()) {
        case "DATE":
            return "DATE";
        case "NUMBER":
            return "NUMBER";
        case "TEXT":
            return "TEXT";
        case "AUTO":
            return "AUTO";
        default:
            return "TEXT";
    }
}
// --- GET: Fetch Mandatory Fields ---
const mandatoryFields = async (req, res) => {
    try {
        const result = await prisma.$queryRawUnsafe(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'track_sheets'
        AND table_schema = 'public'
    `);
        const excludedFields = ["createdAt", "updatedAt", "id"];
        const fields = result
            .map((col) => col.column_name)
            .filter((field) => !excludedFields.includes(field))
            .map((field) => field
            .split("_")
            .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
            .join(" "))
            .sort((a, b) => a.localeCompare(b));
        res.json({ mandatoryFields: fields });
    }
    catch (err) {
        console.error("Error fetching TrackSheets columns:", err);
        res.status(500).json({ error: "Failed to get mandatory fields" });
    }
};
exports.mandatoryFields = mandatoryFields;
// --- POST: Add Custom Fields ---
const createCustomField = async (req, res) => {
    try {
        const { fields } = req.body;
        if (!Array.isArray(fields) || fields.length === 0) {
            return res.status(400).json({ error: "No custom fields provided." });
        }
        const validFields = fields.filter((f) => f.name && f.type);
        if (validFields.length === 0) {
            return res.status(400).json({ error: "No valid custom fields found." });
        }
        const createdFields = [];
        const connectedFieldIds = [];
        let skipped = 0;
        for (const field of validFields) {
            const exists = await prisma.customField.findFirst({
                where: {
                    name: {
                        equals: field.name,
                        mode: "insensitive",
                    },
                },
            });
            if (!exists) {
                const mappedType = mapFieldType(field.type);
                const createData = {
                    name: field.name,
                    type: mappedType,
                    createdBy: field.created_by || "system",
                    updatedBy: field.updated_by || "system",
                };
                if (mappedType === "AUTO") {
                    createData.autoOption = field.autoOption || null;
                }
                const created = await prisma.customField.create({
                    data: createData,
                });
                createdFields.push(created);
                connectedFieldIds.push(created.id);
            }
            else {
                skipped++;
            }
        }
        // Note: Custom fields are now managed through client_custom_field_arrangements table
        // No automatic linking to clients when creating new fields
        return res.status(201).json({ created: createdFields, skipped });
    }
    catch (error) {
        console.error("Error creating custom fields:", error);
        return res.status(500).json({ error: "Server error" });
    }
};
exports.createCustomField = createCustomField;
// --- GET: Fetch All Custom Fields ---
const getCustomFields = async (_req, res) => {
    try {
        const fields = await prisma.customField.findMany({
            orderBy: { createdAt: "desc" },
            include: {
            //autoOption: true, // if you want to return associated AUTO options
            },
        });
        res.status(200).json(fields);
    }
    catch (error) {
        console.error("Error fetching custom fields:", error);
        res.status(500).json({ error: "Server error" });
    }
};
exports.getCustomFields = getCustomFields;
// --- GET: Fetch All Custom Fields with Client Usage ---
const getCustomFieldsWithClients = async (req, res) => {
    try {
        // Extract search parameters
        const { page = "1", pageSize = "50", name, type, "Custom Field Name": customFieldName, "Field Type": fieldType, "Client List": clientList } = req.query;
        const pageNumber = parseInt(page);
        const pageSizeNumber = parseInt(pageSize);
        // Build where conditions for filtering
        const whereConditions = {};
        // Search by custom field name
        if (name || customFieldName) {
            const searchTerm = name || customFieldName;
            whereConditions.name = {
                contains: searchTerm,
                mode: "insensitive"
            };
        }
        // First, get ALL fields that match the basic where conditions (without pagination)
        const allFields = await prisma.customField.findMany({
            where: whereConditions,
            orderBy: { createdAt: "asc" },
            include: {
                ClientCustomFieldArrangement: {
                    include: {
                        Client: {
                            select: {
                                id: true,
                                client_name: true,
                                ownership: {
                                    select: {
                                        username: true
                                    }
                                },
                                associate: {
                                    select: {
                                        name: true
                                    }
                                },
                                branch: {
                                    select: {
                                        branch_name: true
                                    }
                                }
                            }
                        }
                    }
                }
            },
        });
        // Transform ALL the data to show custom fields with their client usage
        let allFieldsWithClients = allFields.map(field => {
            // Create the formatted field type for search purposes
            let formattedType;
            if (field.type === "AUTO" && field.autoOption) {
                formattedType = `Auto - ${field.autoOption.charAt(0).toUpperCase() + field.autoOption.slice(1).toLowerCase()}`;
            }
            else if (field.type) {
                formattedType = field.type.charAt(0).toUpperCase() + field.type.slice(1).toLowerCase();
            }
            else {
                formattedType = "Text";
            }
            return {
                id: field.id,
                name: field.name,
                type: field.type,
                formattedType: formattedType,
                autoOption: field.autoOption,
                createdAt: field.createdAt,
                createdBy: field.createdBy,
                updatedAt: field.updatedAt,
                updatedBy: field.updatedBy,
                clients: field.ClientCustomFieldArrangement.map(arrangement => arrangement.Client),
                clientCount: field.ClientCustomFieldArrangement.length
            };
        });
        // Apply additional filters BEFORE pagination
        // Filter by field type if specified (search in formatted type)
        if (type || fieldType) {
            const searchTerm = (type || fieldType);
            allFieldsWithClients = allFieldsWithClients.filter(field => {
                return field.formattedType.toLowerCase().includes(searchTerm.toLowerCase());
            });
        }
        // Filter by client list if specified
        if (clientList) {
            const searchTerm = clientList.toLowerCase();
            allFieldsWithClients = allFieldsWithClients.filter(field => {
                const clientNames = field.clients.map(client => client.client_name.toLowerCase()).join(", ");
                return clientNames.includes(searchTerm);
            });
        }
        // Calculate the total count AFTER all filtering
        const totalFilteredCount = allFieldsWithClients.length;
        const totalPages = Math.ceil(totalFilteredCount / pageSizeNumber);
        // Apply pagination AFTER filtering
        const skip = (pageNumber - 1) * pageSizeNumber;
        const paginatedFields = allFieldsWithClients.slice(skip, skip + pageSizeNumber);
        res.status(200).json({
            data: paginatedFields,
            datalength: totalFilteredCount,
            page: pageNumber,
            pageSize: pageSizeNumber,
            totalPages: totalPages
        });
    }
    catch (error) {
        console.error("Error fetching custom fields with clients:", error);
        res.status(500).json({ error: "Server error" });
    }
};
exports.getCustomFieldsWithClients = getCustomFieldsWithClients;
// --- POST: Save Selected Fields for a Client ---
const createClientCustomField = async (req, res) => {
    try {
        const { client_id, custom_fields } = req.body;
        // Input validation:
        // - client_id must be present
        // - custom_fields must be an array
        if (!client_id || !Array.isArray(custom_fields)) {
            return res.status(400).json({ error: "Invalid input format." });
        }
        console.log("Logging Client ID:", client_id);
        console.log("Custom Field IDs:", custom_fields);
        // Validate that all custom fields exist
        const foundFields = await prisma.customField.findMany({
            where: { id: { in: custom_fields } },
        });
        console.log("Found Fields:", foundFields);
        if (foundFields.length !== custom_fields.length) {
            return res.status(400).json({ error: "Some custom fields do not exist." });
        }
        // Use transaction to ensure all operations succeed or fail together
        const result = await prisma.$transaction(async (tx) => {
            console.log(`Syncing arrangements for client_id: ${client_id}`);
            // First, delete all existing arrangements for this client
            await tx.clientCustomFieldArrangement.deleteMany({
                where: { client_id: Number(client_id) }
            });
            console.log('Existing arrangements deleted');
            // Then, create new arrangement entries for the current custom fields
            const createdArrangements = [];
            if (custom_fields.length > 0) {
                for (let i = 0; i < custom_fields.length; i++) {
                    const arrangement = await tx.clientCustomFieldArrangement.create({
                        data: {
                            client_id: Number(client_id),
                            custom_field_id: custom_fields[i],
                            order: i + 1, // 1-based ordering
                        },
                        include: {
                            CustomField: {
                                select: {
                                    id: true,
                                    name: true,
                                    type: true,
                                }
                            }
                        }
                    });
                    createdArrangements.push(arrangement);
                    console.log(`✓ Created arrangement: client_id=${client_id}, custom_field_id=${custom_fields[i]}, order=${i + 1}`);
                }
                console.log('All new arrangements created successfully');
            }
            else {
                console.log('No custom fields to create arrangements for');
            }
            return createdArrangements;
        });
        console.log("Created arrangements:", result);
        res.status(201).json({ success: true, arrangements: result });
    }
    catch (err) {
        console.error("Error saving client custom fields:", err.message || err);
        res.status(500).json({
            error: "Server error",
            details: err.message || err,
        });
    }
};
exports.createClientCustomField = createClientCustomField;
// --- GET: Fetch Custom Fields by Client ID ---
const getClientCustomFieldsByClientId = async (req, res) => {
    const client_id = parseInt(req.params.clientId);
    if (isNaN(client_id)) {
        return res.status(400).json({ error: "Invalid client ID" });
    }
    try {
        console.log('Attempting to retrieve custom fields for client_id:', client_id);
        // Get custom fields directly from arrangements table
        const arrangements = await prisma.clientCustomFieldArrangement.findMany({
            where: { client_id: client_id },
            include: {
                CustomField: {
                    select: {
                        id: true,
                        name: true,
                        type: true,
                    }
                },
            },
            orderBy: { order: 'asc' },
        });
        if (arrangements.length === 0) {
            console.log('No custom fields found for client');
            return res.status(200).json({ props: [], custom_fields: [] });
        }
        const customFields = arrangements.map(arr => arr.CustomField);
        console.log('Found custom fields:', customFields.map(f => ({ id: f.id, name: f.name })));
        return res.status(200).json({
            props: [], // No longer using props
            custom_fields: customFields,
        });
    }
    catch (error) {
        console.error("Error fetching client custom fields:", error);
        return res.status(500).json({ error: "Server error" });
    }
};
exports.getClientCustomFieldsByClientId = getClientCustomFieldsByClientId;
// Post /api/client-custom-fields/order
const updateCustomFieldOrder = async (req, res) => {
    try {
        const { client_id, custom_fields } = req.body;
        if (!client_id || !Array.isArray(custom_fields)) {
            return res
                .status(400)
                .json({ error: "Missing client_id or invalid custom_fields" });
        }
        // First, get all the custom fields to ensure they exist
        const allFields = await prisma.customField.findMany({
            where: {
                id: {
                    in: custom_fields
                }
            }
        });
        // Map of field IDs to ensure they all exist
        const fieldMap = new Map(allFields.map(field => [field.id, field]));
        const validFieldIds = custom_fields.filter(id => fieldMap.has(id));
        if (validFieldIds.length === 0) {
            return res.status(400).json({ error: "No valid custom fields provided" });
        }
        console.log('Saving custom fields in order:', validFieldIds);
        // Save the arrangement in the client_custom_field_arrangements table
        const updatedArrangements = await prisma.$transaction(async (tx) => {
            console.log(`Deleting existing arrangements for client_id: ${client_id}`);
            // First, delete all existing arrangements for this client
            await tx.clientCustomFieldArrangement.deleteMany({
                where: { client_id: client_id }
            });
            console.log('Existing arrangements deleted, now storing fresh entries...');
            // Then, create new arrangement entries with explicit order
            const createdArrangements = [];
            for (let i = 0; i < validFieldIds.length; i++) {
                const arrangement = await tx.clientCustomFieldArrangement.create({
                    data: {
                        client_id: client_id,
                        custom_field_id: validFieldIds[i],
                        order: i + 1, // 1-based ordering
                    },
                    include: {
                        CustomField: {
                            select: {
                                id: true,
                                name: true,
                                type: true,
                            }
                        }
                    }
                });
                createdArrangements.push(arrangement);
                console.log(`✓ Created arrangement: client_id=${client_id}, custom_field_id=${validFieldIds[i]}, order=${i + 1}`);
            }
            console.log('All arrangements created successfully');
            return createdArrangements;
        });
        console.log('Successfully saved custom field order');
        res.status(200).json({ success: true, arrangements: updatedArrangements });
    }
    catch (err) {
        console.error("Error updating custom field order:", err);
        res.status(500).json({ error: "Server error", details: err });
    }
};
exports.updateCustomFieldOrder = updateCustomFieldOrder;
//# sourceMappingURL=custom-fields.js.map