"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.upload = void 0;
const express_1 = require("express");
const authentication_1 = require("../../../middleware/authentication");
const view_1 = require("../../controllers/trackSheetImport/view");
const multer_1 = __importDefault(require("multer"));
const create_1 = require("../../controllers/trackSheetImport/create");
const trackSheetImportTemplate_1 = require("../../controllers/trackSheetImport/trackSheetImportTemplate");
const trackSheetImporDownloadById_1 = require("../../controllers/trackSheetImport/trackSheetImporDownloadById");
const router = (0, express_1.Router)();
const DIR = "./src/public";
const storage = multer_1.default.diskStorage({
    destination: function (req, file, cb) {
        cb(null, DIR);
    },
    filename: function (req, file, cb) {
        cb(null, Date.now() + "-" + file.originalname);
    },
});
exports.upload = (0, multer_1.default)({
    storage: storage,
});
router.get("/", authentication_1.authenticate, view_1.trackSheetImportView);
router.get("/:id/errors", authentication_1.authenticate, view_1.getTrackSheetImportErrors);
router.get("/:id/tracksheets", authentication_1.authenticate, view_1.getTrackSheetsByImportId);
router.post("/upload", exports.upload.single("file"), create_1.createTrackSheetImport);
router.get("/template", trackSheetImportTemplate_1.downloadTrackSheetTemplate);
router.get("/download/:id", 
// authenticate,
trackSheetImporDownloadById_1.downloadTrackSheetImportById);
exports.default = router;
//# sourceMappingURL=trackSheetImportRoutes.js.map