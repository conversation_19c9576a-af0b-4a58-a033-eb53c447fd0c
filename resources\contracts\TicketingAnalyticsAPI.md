# Ticketing Analytics API Contracts

## Overview
This document defines the API contracts for the ticketing analytics and reporting system.

## Common Query Parameters
All analytics endpoints support the following optional query parameters for filtering:

- `dateFrom`: ISO 8601 date string (e.g., "2024-01-01T00:00:00Z")
- `dateTo`: ISO 8601 date string (e.g., "2024-12-31T23:59:59Z")
- `tags`: Comma-separated list of tag IDs (e.g., "tag1,tag2,tag3")
- `assignedTo`: User ID or username for assignment filtering
- `stageId`: Pipeline stage ID for stage-specific filtering
- `priority`: Ticket priority level (HIGH, MEDIUM, LOW)
- `corporationId`: Corporation ID for multi-tenancy

## API Endpoints

### 1. Tickets Analytics Overview
**Endpoint:** `GET /api/tickets/analytics/overview`

**Description:** Provides high-level metrics and KPIs for tickets.

**Request:**
```http
GET /api/tickets/analytics/overview?dateFrom=2024-01-01&dateTo=2024-12-31&corporationId=1
```

**Response:**
```json
{
  "success": true,
  "data": {
    "totalTickets": 1250,
    "closedTickets": 1000,
    "openTickets": 250,
    "averageTimeToClose": 145.5,
    "averageTimeToCloseUnit": "hours",
    "closureRate": 80.0,
    "ticketsCreatedInPeriod": 300,
    "ticketsClosedInPeriod": 280
  }
}
```

### 2. Distribution by Stage
**Endpoint:** `GET /api/tickets/analytics/distribution-by-stage`

**Description:** Shows ticket distribution across pipeline stages.

**Request:**
```http
GET /api/tickets/analytics/distribution-by-stage?corporationId=1
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "stageId": "stage-1",
      "stageName": "To Do",
      "stageOrder": 1,
      "ticketCount": 150,
      "percentage": 12.0
    },
    {
      "stageId": "stage-2",
      "stageName": "In Progress",
      "stageOrder": 2,
      "ticketCount": 75,
      "percentage": 6.0
    },
    {
      "stageId": "stage-3",
      "stageName": "Done",
      "stageOrder": 3,
      "ticketCount": 1025,
      "percentage": 82.0
    }
  ]
}
```

### 3. Closure Rate by Stage
**Endpoint:** `GET /api/tickets/analytics/closure-rate-by-stage`

**Description:** Success/completion rate for each pipeline stage.

**Request:**
```http
GET /api/tickets/analytics/closure-rate-by-stage?dateFrom=2024-01-01&corporationId=1
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "stageId": "stage-1",
      "stageName": "To Do",
      "stageOrder": 1,
      "ticketsEntered": 300,
      "ticketsCompleted": 285,
      "closureRate": 95.0,
      "averageTimeInStage": 24.5,
      "timeUnit": "hours"
    },
    {
      "stageId": "stage-2", 
      "stageName": "In Progress",
      "stageOrder": 2,
      "ticketsEntered": 285,
      "ticketsCompleted": 270,
      "closureRate": 94.7,
      "averageTimeInStage": 72.3,
      "timeUnit": "hours"
    }
  ]
}
```

### 4. Average Time by Stage
**Endpoint:** `GET /api/tickets/analytics/average-time-by-stage`

**Description:** Average time spent in each pipeline stage.

**Request:**
```http
GET /api/tickets/analytics/average-time-by-stage?stageId=stage-1&corporationId=1
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "stageId": "stage-1",
      "stageName": "To Do",
      "stageOrder": 1,
      "averageTime": 24.5,
      "medianTime": 18.2,
      "minTime": 2.1,
      "maxTime": 168.0,
      "timeUnit": "hours",
      "sampleSize": 285
    },
    {
      "stageId": "stage-2",
      "stageName": "In Progress", 
      "stageOrder": 2,
      "averageTime": 72.3,
      "medianTime": 64.0,
      "minTime": 8.5,
      "maxTime": 240.0,
      "timeUnit": "hours",
      "sampleSize": 270
    }
  ]
}
```

### 5. Closure Rate Insights
**Endpoint:** `GET /api/tickets/analytics/insights/closure-rate`

**Description:** Insights on closure rate performance with categorization.

**Request:**
```http
GET /api/tickets/analytics/insights/closure-rate?corporationId=1
```

**Response:**
```json
{
  "success": true,
  "data": {
    "overallClosureRate": 80.0,
    "insights": [
      {
        "category": "HIGH_PERFORMER",
        "stages": [
          {
            "stageId": "stage-1",
            "stageName": "To Do",
            "closureRate": 95.0,
            "recommendation": "Excellent performance, maintain current processes"
          }
        ]
      },
      {
        "category": "MEDIUM_PERFORMER", 
        "stages": [
          {
            "stageId": "stage-2",
            "stageName": "In Progress",
            "closureRate": 75.5,
            "recommendation": "Consider process improvements or additional resources"
          }
        ]
      },
      {
        "category": "LOW_PERFORMER",
        "stages": [
          {
            "stageId": "stage-3",
            "stageName": "Review",
            "closureRate": 45.2,
            "recommendation": "Requires immediate attention and process review"
          }
        ]
      }
    ]
  }
}
```

### 6. Resolution Time Analysis
**Endpoint:** `GET /api/tickets/analytics/insights/resolution-time`

**Description:** Analysis of ticket resolution times with actionable insights.

**Request:**
```http
GET /api/tickets/analytics/insights/resolution-time?priority=HIGH&corporationId=1
```

**Response:**
```json
{
  "success": true,
  "data": {
    "overallStats": {
      "averageResolutionTime": 145.5,
      "medianResolutionTime": 120.0,
      "timeUnit": "hours"
    },
    "byPriority": [
      {
        "priority": "HIGH",
        "averageTime": 48.2,
        "medianTime": 36.0,
        "sampleSize": 125,
        "performance": "GOOD",
        "benchmark": 72.0
      },
      {
        "priority": "MEDIUM", 
        "averageTime": 120.5,
        "medianTime": 96.0,
        "sampleSize": 580,
        "performance": "AVERAGE",
        "benchmark": 120.0
      },
      {
        "priority": "LOW",
        "averageTime": 240.8,
        "medianTime": 192.0, 
        "sampleSize": 545,
        "performance": "SLOW",
        "benchmark": 168.0
      }
    ],
    "recommendations": [
      "High priority tickets are meeting SLA requirements",
      "Medium priority tickets performance is within acceptable range",
      "Low priority tickets are taking longer than benchmark - consider resource allocation"
    ]
  }
}
```

## Error Responses

All endpoints follow standard error response format:

```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information (development only)"
}
```

**HTTP Status Codes:**
- `200`: Success
- `400`: Bad Request (invalid parameters)
- `404`: Resource Not Found
- `500`: Internal Server Error

## Performance Notes

- All analytics endpoints implement caching where appropriate
- Large datasets are paginated using standard pagination parameters
- Complex aggregations are optimized with proper indexing
- Query timeout is set to 30 seconds for analytics endpoints
