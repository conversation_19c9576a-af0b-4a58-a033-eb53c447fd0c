model TicketStageTimeTracking {
    id            String   @id @default(uuid())
    
    ticketId      String   @map("ticket_id")
    ticket        Ticket   @relation(fields: [ticketId], references: [id], onDelete: Cascade)
    
    stageId       String   @map("stage_id")
    stage         PipelineStage @relation(fields: [stageId], references: [id], onDelete: Cascade)
    
    startTime     DateTime @map("start_time")
    endTime       DateTime? @map("end_time")
    duration      Int?     @map("duration") // Duration in minutes
    
    assignedTo    String?  @map("assigned_to") @db.VarChar()
    
    createdAt     DateTime @default(now()) @map("created_at")
    createdBy     String?  @map("created_by") @db.VarChar()
    updatedAt     DateTime? @updatedAt @map("updated_at")
    updatedBy     String?  @map("updated_by") @db.VarChar()
    deletedAt     DateTime? @map("deleted_at")
    deletedBy     String?  @map("deleted_by") @db.Var<PERSON><PERSON>()

    @@index([ticketId])
    @@index([stageId])
    @@index([assignedTo])
    @@index([startTime])
    @@index([endTime])
    @@map("ticket_stage_time_trackings")
}
