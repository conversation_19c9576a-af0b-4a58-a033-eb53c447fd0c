"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.FIELD_OPTIONS = void 0;
exports.formatDate = formatDate;
exports.formatFileName = formatFileName;
exports.generateFilePath = generateFilePath;
exports.FIELD_OPTIONS = [
    "ASSOCIATE",
    "CLIENT",
    "ADDITIONALFOLDERNAME",
    "CARRIER",
    "YEAR",
    "MONTH",
    "RECEIVE DATE",
    "FTP FILE NAME",
];
const isField = (value) => exports.FIELD_OPTIONS.includes(value.toUpperCase());
const DEFAULT_OPTIONS = {
    defaultExtension: ".pdf",
    timeZone: "UTC",
    prismaClient: prisma,
    throwOnError: false,
};
function formatDate(date, timeZone) {
    const formatter = new Intl.DateTimeFormat("en-CA", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
        timeZone,
    });
    const parts = formatter.formatToParts(date);
    const year = parts.find(p => p.type === "year")?.value || "";
    const monthNum = parts.find(p => p.type === "month")?.value || "";
    const day = parts.find(p => p.type === "day")?.value || "";
    const dateStr = `${year}-${monthNum}-${day}`;
    const monthText = date.toLocaleString("default", {
        month: "long",
        timeZone,
    }).toUpperCase();
    return { year, month: monthText, dateStr };
}
function formatFileName(fileName, defaultExtension) {
    if (!fileName)
        return "";
    return fileName.endsWith(defaultExtension)
        ? fileName
        : `${fileName}${defaultExtension}`;
}
// Helper to strip .pdf extension from a file name
function stripPdfExtension(fileName) {
    if (!fileName)
        return "";
    return fileName.replace(/\.pdf$/i, "");
}
async function generateFilePath(row, options) {
    const mergedOptions = { ...DEFAULT_OPTIONS, ...options };
    const { prismaClient, timeZone, defaultExtension, throwOnError } = mergedOptions;
    try {
        // Get client info first
        const client = await prismaClient.client.findFirst({
            where: { client_name: row["Client"] },
            select: {
                id: true,
                client_name: true,
                associate: {
                    select: {
                        name: true,
                    },
                },
            },
        });
        // Get the custom file path configuration for this client
        const pathConfig = await prismaClient.clientFTPFilePathConfig.findUnique({
            where: { clientId: client.id },
        });
        // Get carrier info
        const carrier = await prismaClient.carrier.findFirst({
            where: { name: row["Carrier"] },
            select: { carrier_2nd_name: true },
        });
        const receivedDate = row["Received Date"]
            ? new Date(new Date(row["Received Date"]).toISOString())
            : new Date();
        // Format dates and filename
        const { year, month, dateStr } = formatDate(receivedDate, timeZone);
        // Check if the pattern already contains .pdf extension
        const patternHasExtension = pathConfig.filePath.includes('.pdf');
        // Always remove .pdf from FTP file name before using
        const rawFtpFileName = stripPdfExtension(row["FTP File Name"]);
        const baseFilename = patternHasExtension
            ? rawFtpFileName
            : formatFileName(rawFtpFileName, defaultExtension);
        // Always use the raw string for RECEIVE DATE
        const receiveDateStr = row["Received Date"] || "";
        // Create replacements object with all possible values
        const replacements = {
            ASSOCIATE: client.associate?.name || "",
            CLIENT: client.client_name,
            CARRIER: carrier.carrier_2nd_name,
            MONTH: month,
            YEAR: year,
            "RECEIVE DATE": receiveDateStr,
            "FTP FILE NAME": baseFilename,
            FILENAME: baseFilename,
            DATE: dateStr,
        };
        let customPath = pathConfig.filePath;
        // Extract all placeholders from the pattern (anything between brackets or standalone words)
        const placeholderRegex = /\[([^\]]+)\]|([A-Z_]+(?:\s+[A-Z_]+)*)(?=\/|\.|$)/g;
        const placeholders = Array.from(customPath.matchAll(placeholderRegex)).map((match) => match[1] || match[2]);
        // Replace placeholders in the pattern
        placeholders.forEach((placeholder) => {
            // Only replace if it's a defined field
            if (!isField(placeholder))
                return;
            const value = replacements[placeholder];
            if (value) {
                if (placeholder === "RECEIVE DATE") {
                    const regex = /RECEIVE\s+DATE(?=\/|\.|$)/gi;
                    customPath = customPath.replace(regex, value);
                }
                else if (placeholder === "FTP FILE NAME") {
                    const regex = new RegExp(`\\[${placeholder}\\]|${placeholder}(?=\/|\\.|$)`, "g");
                    customPath = customPath.replace(regex, value);
                }
                else {
                    const regex = new RegExp(`\\[${placeholder}\\]|${placeholder}(?=\/|\\.|$)`, "g");
                    customPath = customPath.replace(regex, value.toUpperCase());
                }
            }
        });
        return customPath;
    }
    catch (error) {
        console.error("Error generating file path:", error);
        return "";
    }
}
//# sourceMappingURL=generateFilePath.js.map