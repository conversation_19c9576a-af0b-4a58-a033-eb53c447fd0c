"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTagsByTicketId = exports.getAllTags = void 0;
const helpers_1 = require("../../../../utils/helpers");
const getAllTags = async (req, res) => {
    try {
        const tags = await prisma.tag.findMany({
            where: {
                deletedAt: null,
            },
            orderBy: {
                createdAt: "desc",
            },
        });
        return res.status(200).json({
            success: true,
            data: tags,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getAllTags = getAllTags;
const getTagsByTicketId = async (req, res) => {
    try {
        const { ticketId } = req.params;
        if (!ticketId) {
            return res.status(400).json({
                success: false,
                message: "Ticket ID is required",
            });
        }
        // Get the ticket with its tags array
        const ticket = await prisma.ticket.findFirst({
            where: {
                id: ticketId,
                deletedAt: null,
            },
            select: {
                tags: true,
            },
        });
        if (!ticket) {
            return res.status(404).json({
                success: false,
                message: "Ticket not found",
            });
        }
        // Get the actual tag objects for the tag IDs
        const tags = await prisma.tag.findMany({
            where: {
                id: {
                    in: ticket.tags,
                },
                deletedAt: null,
            },
        });
        return res.status(200).json({
            success: true,
            data: tags,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.getTagsByTicketId = getTagsByTicketId;
//# sourceMappingURL=view.js.map