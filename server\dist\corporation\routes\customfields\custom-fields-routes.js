"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const view_1 = require("../../controllers/customfields/clientcustomfields/view");
const create_1 = require("../../controllers/customfields/clientcustomfields/create");
const view_2 = require("../../controllers/customfields/mandatoryfields/view");
const view_3 = require("../../controllers/customfields/view");
const create_2 = require("../../controllers/customfields/create");
const update_1 = require("../../controllers/customfields/clientcustomfields/update");
const router = (0, express_1.Router)();
router.get("/client-custom-fields/:clientId", view_1.getClientCustomFieldsByClientId);
router.get("/custom-fields-with-clients", view_1.getCustomFieldsWithClients);
//client custom fields
router.post("/client-custom-fields", create_1.createClientCustomField);
//Mandatory fields
router.get("/mandatory-fields", view_2.mandatoryFields);
//custom fields
router.post("/custom-fields", create_2.createCustomField);
router.get("/custom-fields", view_3.getCustomFields);
//client custom fields arrangement
router.post("/client-custom-fields/order", update_1.updateCustomFieldOrder);
exports.default = router;
//# sourceMappingURL=custom-fields-routes.js.map