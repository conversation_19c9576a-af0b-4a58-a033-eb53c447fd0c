"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteTag = void 0;
const helpers_1 = require("../../../../utils/helpers");
const deleteTag = async (req, res) => {
    try {
        const { id: tagId } = req.params;
        const { deletedBy } = req.body;
        if (!tagId) {
            return res.status(400).json({
                success: false,
                message: "Tag ID is required",
            });
        }
        // Only use deletedBy from req.body or fallback to 'system'
        let username = deletedBy || "system";
        const tag = await prisma.tag.update({
            where: { id: tagId },
            data: {
                deletedAt: new Date(),
                deletedBy: username,
            },
        });
        return res.status(200).json({
            success: true,
            message: "Tag deleted successfully",
            data: tag,
        });
    }
    catch (error) {
        return (0, helpers_1.handleError)(res, error);
    }
};
exports.deleteTag = deleteTag;
//# sourceMappingURL=delete.js.map