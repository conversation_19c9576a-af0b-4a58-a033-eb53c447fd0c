"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const userroutes_1 = __importDefault(require("./corporation/routes/userroutes"));
const clientroutes_1 = __importDefault(require("./corporation/routes/client/clientroutes"));
const corporationroutes_1 = __importDefault(require("./corporation/routes/corporationroutes"));
const workreportroutes_1 = __importDefault(require("./corporation/routes/workreport/workreportroutes"));
const carrierroutes_1 = __importDefault(require("./corporation/routes/carrier/carrierroutes"));
const superadminroutes_1 = __importDefault(require("./superadmin/routes/superadminroutes"));
const worktyperoutes_1 = __importDefault(require("./corporation/routes/worktype/worktyperoutes"));
const dailyPlanningRoutes_1 = __importDefault(require("./corporation/routes/dailyPlanning/dailyPlanningRoutes"));
const dailyPlanningDetailsRoutes_1 = __importDefault(require("./corporation/routes/dailyPlanningDetails/dailyPlanningDetailsRoutes"));
const uploadfilerouter_1 = __importDefault(require("./corporation/routes/uploadFile/uploadfilerouter"));
const categoryroutes_1 = __importDefault(require("./corporation/routes/category/categoryroutes"));
const branchroutes_1 = __importDefault(require("./corporation/routes/branch/branchroutes"));
const searchRoutes_1 = __importDefault(require("./search/searchRoutes"));
const cors_1 = __importDefault(require("cors"));
const cookie_parser_1 = __importDefault(require("cookie-parser"));
const clientCarrierRoutes_1 = __importDefault(require("./corporation/routes/clientCarrier/clientCarrierRoutes"));
const rolesPermissionRoutes_1 = __importDefault(require("./corporation/routes/rolesPermission/rolesPermissionRoutes"));
require("./corporation/controllers/uploadfile/incrementAge");
const usertitleroutes_1 = __importDefault(require("./corporation/routes/usertitle/usertitleroutes"));
const crypto = require("crypto");
const associateroutes_1 = __importDefault(require("./corporation/routes/associate/associateroutes"));
const customizeReportRoutes_1 = __importDefault(require("./corporation/routes/customizeReport/customizeReportRoutes"));
const custom_fields_routes_1 = __importDefault(require("./corporation/routes/customfields/custom-fields-routes"));
const trackSheetsRoutes_1 = __importDefault(require("./corporation/routes/trackSheets/trackSheetsRoutes"));
const ClientCustomFieldsRoute_1 = __importDefault(require("./corporation/routes/clientCustomFields/ClientCustomFieldsRoute"));
const legrandMappingsRoutes_1 = __importDefault(require("./corporation/routes/legrandMappings/legrandMappingsRoutes"));
const manualMatchingMappingRoutes_1 = __importDefault(require("./corporation/routes/manualMatchingMapping/manualMatchingMappingRoutes"));
const trackSheetImportRoutes_1 = __importDefault(require("./corporation/routes/trackSheetImport/trackSheetImportRoutes"));
const customFilepathRoutes_1 = __importDefault(require("./corporation/routes/customFilepath/customFilepathRoutes"));
const pipelineRoutes_1 = __importDefault(require("./corporation/routes/pipeline/pipelineRoutes"));
const pipelineStageRoutes_1 = __importDefault(require("./corporation/routes/pipelineStage/pipelineStageRoutes"));
const ticketRoutes_1 = __importDefault(require("./corporation/routes/ticket/ticketRoutes"));
const commentRoutes_1 = __importDefault(require("./corporation/routes/ticket/commentRoutes"));
const tagRoutes_1 = __importDefault(require("./corporation/routes/ticket/tagRoutes"));
const analyticsRoutes_1 = __importDefault(require("./corporation/routes/ticket/analyticsRoutes"));
const app = (0, express_1.default)();
app.use(express_1.default.json());
app.use((0, cors_1.default)({
    origin: "http://localhost:3000",
    credentials: true,
}));
const jwtSecret = crypto.randomBytes(32).toString("hex");
app.use(express_1.default.json({ limit: "150mb" }));
app.use(express_1.default.urlencoded({ limit: "150mb", extended: true }));
app.use((0, cookie_parser_1.default)());
app.use("/api/users", userroutes_1.default);
app.use("/api/clients", clientroutes_1.default);
app.use("/api/corporation", corporationroutes_1.default);
app.use("/api/workreport", workreportroutes_1.default);
app.use("/api/carrier", carrierroutes_1.default);
app.use("/api/superAdmin", superadminroutes_1.default);
app.use("/api/worktype", worktyperoutes_1.default);
app.use("/api/client-carrier", clientCarrierRoutes_1.default);
app.use("/api/category", categoryroutes_1.default);
app.use("/api/branch", branchroutes_1.default);
app.use("/api/client-carreir", clientCarrierRoutes_1.default);
app.use("/api/rolespermission", rolesPermissionRoutes_1.default);
app.use("/api/dailyplanning", dailyPlanningRoutes_1.default);
app.use("/api/dailyplanningdetails", dailyPlanningDetailsRoutes_1.default);
app.use("/api/upload", uploadfilerouter_1.default);
app.use("/api/usertitle", usertitleroutes_1.default);
app.use("/api/search", searchRoutes_1.default);
app.use("/api/associate", associateroutes_1.default);
app.use("/api/customizeReport", customizeReportRoutes_1.default);
app.use("/api", custom_fields_routes_1.default);
app.use("/api/track-sheets", trackSheetsRoutes_1.default);
app.use("/api/client-custom-fields", ClientCustomFieldsRoute_1.default);
app.use("/api/legrand-mappings", legrandMappingsRoutes_1.default);
app.use("/api/manual-matching-mappings", manualMatchingMappingRoutes_1.default);
app.use("/api/track-sheet-import", trackSheetImportRoutes_1.default);
app.use("/api/custom-filepath", customFilepathRoutes_1.default);
app.use("/api/pipelines", pipelineRoutes_1.default);
app.use("/api/pipeline-stages", pipelineStageRoutes_1.default);
app.use("/api/tickets", ticketRoutes_1.default);
app.use("/api/comments", commentRoutes_1.default);
app.use("/api/tags", tagRoutes_1.default);
app.use("/api/analytics", analyticsRoutes_1.default);
app.get("/", (req, res) => {
    res.send("Welcome to the API!");
});
exports.default = app;
//# sourceMappingURL=app.js.map