"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteClientFTPFilePathConfig = void 0;
const prismaClient_1 = __importDefault(require("../../../utils/prismaClient"));
const deleteClientFTPFilePathConfig = async (req, res) => {
    try {
        const { clientId, filePath } = req.query;
        if (!clientId || !filePath) {
            return res.status(400).json({ error: "clientId and filePath are required" });
        }
        // Find the record first
        const record = await prismaClient_1.default.clientFTPFilePathConfig.findFirst({
            where: {
                clientId: Number(clientId),
                filePath: String(filePath),
            },
        });
        if (!record) {
            return res.status(404).json({ error: "Record not found" });
        }
        // Delete by clientId (primary key)
        await prismaClient_1.default.clientFTPFilePathConfig.delete({
            where: {
                clientId: Number(clientId),
            },
        });
        return res.status(200).json({ success: true });
    }
    catch (error) {
        return res.status(500).json({ error: error.message });
    }
};
exports.deleteClientFTPFilePathConfig = deleteClientFTPFilePathConfig;
exports.default = exports.deleteClientFTPFilePathConfig;
//# sourceMappingURL=delete.js.map